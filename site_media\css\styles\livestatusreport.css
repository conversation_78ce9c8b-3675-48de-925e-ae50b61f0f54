body,div,h1{font-family:'times new roman', verdana, arial;margin:0;padding:0;}
body{background-color:#fff;color:#333;margin:0;padding:0;}
h1{font-size:large;font-weight:400;margin:0;}
h2{color:#333;font-size:small;font-weight:400;margin:0;}
.demo{width:600px;margin:20px auto;}
.demo h1,.demo h1 a{font-size:120%;text-align:center;text-decoration:none;color:#000;}
.demo p{text-align:center;}
.demo table.tablesorter{font-size:14px;}
table.info{border:#000 1px solid;border-collapse:collapse;margin:10px 0 0 10px;}
table.tablesorter table.info tbody th,table.tablesorter table.info td{border:#000 1px solid;}
table.tablesorter table.info tbody th{background:#eee;}
table.options{width:100%;}
table.options pre{width:95%;}
table.options .property{width:115px;}
table.options .type{width:80px; }
table.options .defaults{width:200px;}
table.options .examples{width:60px;}
table.compatibility { width: 50%; float: right; font-size: .8em; margin-left: 20px; }
table.compatibility th,table.compatibility td { text-align: center; padding: 2px; }
pre,#display{overflow-x:auto;padding:15px;border:1px solid #ddd;border-left-width:5px;}
pre,#display,code{background-color:#eee;color:#333;font-size:small;list-style:none;}
code{padding: 1px 5px;}
a code {text-decoration:underline;}
pre.normal{background-color:transparent;border:none;border-left-width:0;overflow-x:auto;}
#logo{background:url(images/jq.png);display:block;float:right;height:31px;margin-right:10px;margin-top:10px;width:110px;}
#main{margin:1 35px 1000px;padding:0 35px 10px 0;clear:both;}
#content{padding:20px;}
#busy{background-color:#e95555;border:1px ridge #ccc;color:#eee;display:none;padding:3px;position:absolute;right:7px;top:7px;}
#start,#case{color:#007baa;}
#demo strong{color:#a00;}
hr{height:1px;}
code{font-size:108%;font-style:normal;padding:0;}
ul{color:#333;list-style:square;}
#banner{margin:20px 20px 5px 20px;padding:0;text-align:left;}
#banner *,.demo h1,.demo h1 em{color:#232121;font-family:Arial, Helvetica, sans-serif;font-size:20px;font-style:normal;font-weight:400;margin:0;padding:0;}
#banner h1{display:block;float:left;}
#banner h1 em,.demo h1 em{color:#6cf;}
#banner h2{float:right;font-size:26px;margin:10px 10px -10px -10px;}
#banner h3{clear:both;display:block;font-size:12px;margin-top:-20px;border-bottom:1px solid #888;}
#banner a{display:block;font-size:14px;margin:5px 0 0;padding:10px 0 0;float:right;}
a.external{background-image:url(../img/external.png);background-position:center right;background-repeat:no-repeat;padding-right:12px;}
form{font-size:10pt;margin-bottom:20px;width:auto;}
form fieldset{padding:10px;text-align:left;width:140px;}
div#main h1{border-bottom:1px solid #CDCDCD;display:block;margin-top:20px;padding:10px 0 2px;}
table#tablesorter-demo {margin: 10px 0 0 0;}
table.options *,table#methods *,table#events *, p.small{font-size:small;}
p.small {padding-left: 25px;}
p.tip em {padding: 2px; background-color: #6cf; color: #fff;}
span.tip em {padding: 0 2px;background-color: #00ce53; color: #fff; font-size:90%; }
div.digg {float: right;}
.next-up { padding-top: 10px; font-size: 90%; }
.narrow-block { width: 50%; margin: 50px auto; }
.spacer { height: 800px; }
.right { text-align:right; }
#pager-demo th.remove { width: 20px; } /* pager demo */
#pager-demo button.remove { width: 20px; height: 20px; font-size: 10px; color: #800; }
.box { width: 48%; min-width: 300px; float: left; padding: 0 1%; }
a.deprecated { color: #a00; }
span.deprecated { background: #a00; color: #fff; padding: 1px 3px; }
.hidden { display: none; }
.clear { clear: both; }
.download { color: #050505; text-decoration: none; padding: 3px 10px; border: 1px solid #ddd; background: -moz-linear-gradient( top, #ddd 0%, #bbb 50%, #aaa 50%, #bbb); background: -webkit-gradient( linear, left top, left bottom, from(#ddd), color-stop(0.50, #bbb), color-stop(0.50, #aaa), to(#bbb)); border-radius: 8px; -moz-border-radius: 8px; -webkit-border-radius: 8px; }
.download:hover { background: -moz-linear-gradient( top, #dddddd 0%, #dddddd 50%, #cccccc 50%, #dddddd); background: -webkit-gradient( linear, left top, left bottom, from(#dddddd), color-stop(0.50, #dddddd), color-stop(0.50, #cccccc), to(#dddddd)); }
.bootstrap_buttons button { margin: 5px 0 0 0; }
#main .ui-accordion-header a { font-size: 14px; margin-left: 24px; }
#main .ui-accordion-content { font-size: 14px; }