/*************
  Grey Theme
 *************/
/* overall */
.tablesorter-grey {
	width: 100%;
	margin: 10px 0 15px;
	text-align: left;
	border-spacing: 0;
	border-left: #555 1px solid;
}

/* header */
.tablesorter-grey th,
.tablesorter-grey thead td {
	font: bold 12px/18px Arial, Sans-serif;
	color: #c8c8c8;
	background-color: #3c3c3c;
	background-image: -moz-linear-gradient(top, #555, #3c3c3c);
	background-image: -ms-linear-gradient(top, #555, #3c3c3c);
	background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#555), to(#3c3c3c));
	background-image: -webkit-linear-gradient(top, #555, #3c3c3c);
	background-image: -o-linear-gradient(top, #555, #3c3c3c);
	background-image: linear-gradient(to bottom, #555,#3c3c3c); 
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#555555', endColorstr='#3c3c3c',GradientType=0 );
	background-repeat: repeat-x;
	border-right: #555 1px solid;
	text-shadow: 0 1px 0 rgba(128, 128, 128, 0.7);
	-webkit-box-shadow: inset 0 1px 0 #222;
	-moz-box-shadow: inset 0 1px 0 #222;
	box-shadow: inset 0 1px 0 #222;
	padding: 4px;
}
.tablesorter-grey .tablesorter-header-inner,
.tablesorter-grey .tablesorter-header-inner {
	position: relative;
	padding: 4px 15px 4px 4px;
}
.tablesorter-grey .header i,
.tablesorter-grey .tablesorter-header i {
	width: 18px;
	height: 10px;
	position: absolute;
	right: 2px;
	top: 50%;
	margin-top: -10px;
	/* white (unsorted) double arrow */
	background-image: url(data:image/gif;base64,R0lGODlhFQAJAIAAAP///////yH5BAEAAAEALAAAAAAVAAkAAAIXjI+AywnaYnhUMoqt3gZXPmVg94yJVQAAOw==);
	background-repeat: no-repeat;
	background-position: center right;
	padding: 4px;
	white-space: normal;
	cursor: pointer;
}
.tablesorter-grey th.headerSortUp,
.tablesorter-grey th.tablesorter-headerSortUp,
.tablesorter-grey th.headerSortDown,
.tablesorter-grey th.tablesorter-headerSortDown {
	color: #ddd;
	background-color: #135185;
	background-image: -moz-linear-gradient(top, #195c93, #0e4776);
	background-image: -ms-linear-gradient(top, #195c93, #0e4776);
	background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#195c93), to(#0e4776));
	background-image: -webkit-linear-gradient(top, #195c93, #0e4776);
	background-image: -o-linear-gradient(top, #195c93, #0e4776);
	background-image: linear-gradient(to bottom, #195c93, #0e4776);
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#195c93', endColorstr='#0e4776',GradientType=0 );
}
.tablesorter-grey .headerSortUp i,
.tablesorter-grey .tablesorter-headerSortUp i,
.tablesorter-grey .tablesorter-headerAsc i {
	/* white asc arrow */
	background-image: url(data:image/gif;base64,R0lGODlhFQAEAIAAAP///////yH5BAEAAAEALAAAAAAVAAQAAAINjI8Bya2wnINUMopZAQA7);
}
.tablesorter-grey .headerSortDown i,
.tablesorter-grey .tablesorter-headerSortDown i,
.tablesorter-grey .tablesorter-headerDesc i {
	/* white desc arrow */
	background-image: url(data:image/gif;base64,R0lGODlhFQAEAIAAAP///////yH5BAEAAAEALAAAAAAVAAQAAAINjB+gC+jP2ptn0WskLQA7);
}
.tablesorter-grey thead .sorter-false i {
	background-image: url();
	padding: 4px;
}

/* tfoot */
.tablesorter-grey tbody td,
.tablesorter-grey tfoot th,
.tablesorter-grey tfoot td {
	padding: 4px;
	vertical-align: top;
	border-right: #555 1px solid;
}
.tablesorter-grey tfoot th,
.tablesorter-grey tfoot td {
	padding: 8px;
}

/* tbody */
.tablesorter-grey td {
	color: #eee;
	background-color: #6d6d6d;
	padding: 4px;
	vertical-align: top;
}

/* hovered row colors
 you'll need to add additional lines for
 rows with more than 2 child rows
 */
.tablesorter-grey tbody > tr:hover > td,
.tablesorter-grey tbody > tr:hover + tr.tablesorter-childRow > td,
.tablesorter-grey tbody > tr:hover + tr.tablesorter-childRow + tr.tablesorter-childRow > td,
.tablesorter-grey tbody > tr.even:hover > td,
.tablesorter-grey tbody > tr.even:hover + tr.tablesorter-childRow > td,
.tablesorter-grey tbody > tr.even:hover + tr.tablesorter-childRow + tr.tablesorter-childRow > td {
	background: #134b78;
}
.tablesorter-grey tbody > tr.odd:hover > td,
.tablesorter-grey tbody > tr.odd:hover + tr.tablesorter-childRow > td,
.tablesorter-grey tbody > tr.odd:hover + tr.tablesorter-childRow + tr.tablesorter-childRow > td {
	background: #134b78;
}

/* table processing indicator */
.tablesorter-grey .tablesorter-processing {
	background-position: center center !important;
	background-repeat: no-repeat !important;
	/* background-image: url(../addons/pager/icons/loading.gif) !important; */
	background-image: url('data:image/gif;base64,R0lGODlhFAAUAKEAAO7u7lpaWgAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQBCgACACwAAAAAFAAUAAACQZRvoIDtu1wLQUAlqKTVxqwhXIiBnDg6Y4eyx4lKW5XK7wrLeK3vbq8J2W4T4e1nMhpWrZCTt3xKZ8kgsggdJmUFACH5BAEKAAIALAcAAAALAAcAAAIUVB6ii7jajgCAuUmtovxtXnmdUAAAIfkEAQoAAgAsDQACAAcACwAAAhRUIpmHy/3gUVQAQO9NetuugCFWAAAh+QQBCgACACwNAAcABwALAAACE5QVcZjKbVo6ck2AF95m5/6BSwEAIfkEAQoAAgAsBwANAAsABwAAAhOUH3kr6QaAcSrGWe1VQl+mMUIBACH5BAEKAAIALAIADQALAAcAAAIUlICmh7ncTAgqijkruDiv7n2YUAAAIfkEAQoAAgAsAAAHAAcACwAAAhQUIGmHyedehIoqFXLKfPOAaZdWAAAh+QQFCgACACwAAAIABwALAAACFJQFcJiXb15zLYRl7cla8OtlGGgUADs=') !important;
}

/* Zebra Widget - row alternating colors */
.tablesorter-grey tbody tr.odd td {
	background-color: #5e5e5e;
}
.tablesorter-grey tbody tr.even td {
	background-color: #6d6d6d;
}

/* Column Widget - column sort colors */
.tablesorter-grey td.primary,
.tablesorter-grey tr.odd td.primary {
	color: #ddd;
	background-color: #165388;
}
.tablesorter-grey tr.even td.primary {
	color: #ddd;
	background-color: #195c93;
}
.tablesorter-grey td.secondary,
.tablesorter-grey tr.odd td.secondary {
	color: #ddd;
	background-color: #185C9A;
}
.tablesorter-grey tr.even td.secondary {
	color: #ddd;
	background-color: #1D67A5;
}
.tablesorter-grey td.tertiary,
.tablesorter-grey tr.odd td.tertiary {
	color: #ddd;
	background-color: #1B67AD;
}
.tablesorter-grey tr.even td.tertiary {
	color: #ddd;
	background-color: #2073B7;
}

/* filter widget */
.tablesorter-grey .tablesorter-filter-row td {
	background: #3c3c3c;
	line-height: normal;
	text-align: center; /* center the input */
	-webkit-transition: line-height 0.1s ease;
	-moz-transition: line-height 0.1s ease;
	-o-transition: line-height 0.1s ease;
	transition: line-height 0.1s ease;
}
/* optional disabled input styling */
.tablesorter-grey .tablesorter-filter-row .disabled {
	opacity: 0.5;
	filter: alpha(opacity=50);
	cursor: not-allowed;
}
/* hidden filter row */
.tablesorter-grey .tablesorter-filter-row.hideme td {
	/*** *********************************************** ***/
	/*** change this padding to modify the thickness     ***/
	/*** of the closed filter row (height = padding x 2) ***/
	padding: 2px;
	/*** *********************************************** ***/
	margin: 0;
	line-height: 0;
	cursor: pointer;
}
.tablesorter-grey .tablesorter-filter-row.hideme .tablesorter-filter {
	height: 1px;
	min-height: 0;
	border: 0;
	padding: 0;
	margin: 0;
	/* don't use visibility: hidden because it disables tabbing */
	opacity: 0;
	filter: alpha(opacity=0);
}
/* filters */
.tablesorter-grey .tablesorter-filter {
	width: 98%;
	height: inherit;
	margin: 0;
	padding: 4px;
	background-color: #6d6d6d;
	border: 1px solid #555;
	color: #ddd;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-transition: height 0.1s ease;
	-moz-transition: height 0.1s ease;
	-o-transition: height 0.1s ease;
	transition: height 0.1s ease;
}
