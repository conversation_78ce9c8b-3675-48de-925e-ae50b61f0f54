<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Schnell SMS Tool</title>
<link rel="stylesheet" type="text/css" href="/site_media/css/styles/style.css" />
<link rel="stylesheet" href="/site_media/js/jquery_ui.css" />
<script src="/site_media/js/jquery.js"></script>
<script src="/site_media/js/jquery_ui.js"></script>
<script src="/site_media/js/cookies.js"></script>
</head>
<script>
$(function(){    
	$(document).ready(function(){	
		var url=window.location.href;
		url = url.substring(7);
		window.urlpath = url.substring(0,url.indexOf('/'));
		$('#msg').val('');
		$('#phoneno').val('');
		$('#phoneno').focus();
	});    
       
	$('#sendsms').click(function () {
		disable();
		if ($('#phoneno').val() == ""){
			alert("Please Enter Valid Phone no");
			$('#phoneno').focus();
			enable();
			return;
		}else if(($('#msg').val() == "")){
			alert("Please Enter Valid Message");
			$('#msg').focus();
			enable();
			return;
		}
		data = $('#phoneno').val() + "~" + $('#msg').val();
		alert(data);
		$.ajax({
			url : "/sms/send/",
			type : "POST",
			dataType: "json",
			data : data ,
			success : function(json) {
				//alert(json);
				$('#result').text(json);
				enable();
				return;			
			},
			error : function(xhr,errmsg,err) {			
				alert(xhr.status + ": " + xhr.responseText);
				enable();
			}
		});
	});
      
});

function KeyCheck(myfield,e){
	var keycode;
	if (window.event) keycode = window.event.keyCode;
	else if (e) keycode = e.which;
	else return true;
	//alert(keycode)
	if ((keycode==40) || (keycode==41) || (keycode==126) || (keycode==38))
		return false;
	else
		return true;
}
function KeyCheckNumber(myfield,e){
	var keycode;
	if (window.event) keycode = window.event.keyCode ;
	else if (e) keycode = e.which;
	else return true;
	if (((keycode>47) && (keycode<58)) || (keycode==8) || (keycode==44) || (keycode==0))
		return true;
	else
		return false;
}

function disable(){
	document.getElementById('sendsms').style.pointerEvents = 'none';
}
function enable(){
	document.getElementById('sendsms').style.pointerEvents = 'auto';
}
</script>
<body>

<!--Top section starts here-->
  <div id="green-top">
    <div class="top-green">
    <div class="welcome-head1" Align="center">SCHNELL GENERAL SMS TOOL</div>
     </div>
  </div>
</div>

<div class="whitecontent-bg">
 <div class = "blue-box2" align="center">
      <table width="600"  cellpadding="5" align="center" cellspacing="10" border="0" >
		<tr>
		<td>&nbsp; Phone No: (eg: 1234564545,1234567890) <input type="text" value="" class="txtname6" id="phoneno" maxlength="150" onKeyPress='return KeyCheckNumber(this,event);'></td>
		<td>&nbsp;Message: (eg: testing*@#) <input type="text" value="" class="txtname6" id="msg" maxlength="150" onKeyPress='return KeyCheck(this,event);'></td>
		<td><a href="javascript:void();" class="load-map" id="sendsms">Send SMS</a></td>
		</tr>
	</table>
	<font color="blue" size='3.5px'><label id='result'></label></font>
    </div>

<br clear="all" />  
<br clear="all" />    
<div class="bdr-bottom1"></div>
<br clear="all" />
</br>
<!--footer section ends here-->
</body>
</html>
