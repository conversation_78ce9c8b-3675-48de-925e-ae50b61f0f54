# lume/routes.py

from fastapi import APIRouter, Request
from tb_service.lume import api

router = APIRouter()


# api routes
router.post("/login/")(api.login)
router.get("/print_qr/")(api.get_current_user_role)
router.get("/get/device/")(api.get_device_details)
router.post("/device/service/")(api.device_take_service)
router.get("/entity/search/")(api.device_detail)
router.get("/entities/")(api.get_entities)
router.get("/entities/search/")(api.entities_search)
router.get("/get/assets/")(api.get_asset_lat_lon)
router.get("/customers/")(api.get_customers)
router.get("/regions/")(api.get_regions)
router.get("/zones/")(api.get_zones)
router.get("/wards/")(api.get_wards)

router.post("/gw/dispatch/")(api.dispatch_gw)
router.post("/gw/install/")(api.gw_install)
router.post("/gw/update/")(api.update_gw_attribute_details)
router.post("/gw/control/")(api.gw_control)
router.post("/gw/replace/")(api.gw_replace)

router.post("/ebmeter/replace/")(api.ebmeter_replace)
router.get("/ebmeter/isinstallable/")(api.ebmeter_isinstallable)

router.post("/ilm/test/")(api.ilm_test_initiate_and_reset)
router.post("/ilm/update/")(api.update_ilm_attribute_details)
router.post("/ilm/control/")(api.ilm_control)

router.post("/pole/install/")(api.pole_install)
router.get("/get/pole/")(api.get_pole)
router.get("/get/entity/count/")(api.get_pole_count)
router.get("/get/user/installation/count/")(api.get_asset_installation_history)

router.post("/luminaire/maintain/")(api.luminaire_maintain)

router.get("/get/project/wattage/")(api.get_project_wise_wattage)

router.post("/image/upload/")(api.image_upload_to_aws)
router.post("/logout/")(api.logout)

router.post("/tickets/serializer/")(api.ticket_name_serializer)

router.get("/lightpoint/commission")(api.commission_lightpoint)
router.post("/lightpoint/location")(api.update_latitude_longitude)

router.post("/login/ppe")(api.upload_ppe_image)
router.post("/attendance/swipes")(api.greyt_hr_swipes)
