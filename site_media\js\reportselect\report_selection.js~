$(function(){  
	$( "#create-user1" )
            .button()
            .click(function() {	
		var seletext="";
		if($("#datepicker").val()  == "" && $("#datepicker1").val()  == "")
		{
			alert("Please select From and to Date");
		}
		else if($("#datepicker1").val()  != "" && $("#datepicker").val()  =="")
		{
			alert("Please select From date");
		}
		else if($("#datepicker").val()  != "" && $("#datepicker1").val()  == "")
		{
			alert("Please select To date");
		}
			
		else
		{	
			if ($("#search_txt").val() == ""){ search_txt = "NO"} else {search_txt = $("#search_txt").val()}
			seletext +=$("#datepicker").val() + "%2C" + $("#datepicker1").val() + "%2C" + search_txt ;
			seletext = seletext.replace(/-/g,'');
	
			var newtab = window.open();
			newtab.location = 'http://'+urlpath+"/ilm/"+seletext +"/";
		}
	});		
	
	$( "#create-user2" )
            .button()
            .click(function() {				    
			var newtab = window.open();
			newtab.location = 'http://'+urlpath+"/ilm/NO,NO,NO/";						    		    
	});		
})
