/*..........Welcome to Schnell............*/

body{background:#ffffff; margin:0px; padding:0px; list-style:none; font-family:Arial, Helvetica, sans-serif; font-size:11px;  color:#7a7979; line-height:20px;}
ul {margin:0px; padding:0px; list-style:none;}
li {margin:0px; padding:0px; list-style:none;}

p{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#7a7979;line-height:20px; text-align:center;}

/*..........TOP SECTION........*/
#bg-top {background:#e9f7f0 repeat-x;width:100%; height:75px; }
.top{ width:1001px; margin:0px auto; height:75px;}
.logo {float:left; display:block; margin-top:4px; margin-left:15px;}
.logo-icon{float:right; display:block; margin:3px 22px 0 0;}

.logo {float:left; display:block; margin-top:4px; margin-left:15px;}
.logo-icon{float:right; display:block; margin:3px 22px 0 0;}

#green-top{background:#60a580 repeat-x; width:100%; height:30px;}
.top-green{width:1001px; margin:0px auto; height:30px;}
.welcome-txt{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#FFFFFF; float:left; padding:5px 0 0 780px;}
a.login{ font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#fba325; float:left; text-decoration:none; padding-top:5px;}
a.login:hover{text-decoration:underline; color:#FFFFFF;}

/*..........CONTENT SECTION........*/
.whitecontent-bg{width:1001px; margin:0px auto;}

h1{ font-family:Trebuchet MS,Arial, Helvetica, sans-serif; font-size:24px; font-weight:bold; color:#009e43; text-align:center; padding-top:10px;}
h2{ font-family:Trebuchet MS,Arial, Helvetica, sans-serif; font-size:20px; font-weight:bold; color:#009e43;margin-top:-40px; padding-left:10px; float:left;}

.login-box{background:url(../../img/login-tab.jpg) no-repeat; width:470px; height:290px; float:left; margin:5px 0 0 266px;}
.login-icon{background:url(../../img/login-icon.jpg) no-repeat; height:27px; font-family:Arial, Helvetica, sans-serif; font-size:14px; font-weight:bold; color:#FFFFFF; float:left; margin:3px 0 0 12px; padding:5px 0 0 29px;}
.login-box1{border-left:1px #c0dece solid; border-right:1px #c0dece solid; margin-top:5px;}
.down-loginimg{background:url(../../img/down-loginimg.jpg) no-repeat; width:470px; height:25px;}

.txtname{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#7a7979; padding-left:69px; margin-top:8px; float:left;}
.textbox{ background:url(../../img/txtbox.jpg) no-repeat; width:252px; height:36px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#7a7979; margin-left:10px; padding-left:12px;border:none;}

.textbox1{background:url(../../img/txtbox.jpg) no-repeat; width:252px; height:36px;padding:10px; border:none; font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#7a7979;margin-left:10px;}

.txtname1{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#7a7979; float:left; padding:20px 0 0 0; }
.txtname2{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#7a7979; width:10px;}
.txtname7{font-family:Arial, Helvetica, sans-serif; font-size:12px; color:#7a7979;  padding:0 0 0 20px; }

.txtname3{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#ffffff; float:left;background:url(../../img/green-line.jpg) no-repeat right 0px; height:30px;line-height:30px; list-style:none; padding:0 35px;}
.txtname4{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#918f8f; float:left; padding:20px 0 0 117px; }
.txtname5{font-family:Arial, Helvetica, sans-serif; font-size:11px; font-weight:bold; color:#bbbbbb; float:left; padding:0 0 0 15px; }
.txtname6{font-family:Arial, Helvetica, sans-serif; font-size:18px; color:#000000; width:350px; height:18px;}

.textbox2{border:1px #a2b5c1 solid; width:105px; height:20px;padding-left:12px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#7a7979; margin-left:0px;margin-top:18px;overflow:hidden; display:block;}
.textbox3{ border:1px #a2b5c1 solid; width:105px; height:20px;padding-left:12px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#7a7979; overflow:hidden; display:block;margin-top:18px;}
.textbox4{ border:1px #a2b5c1 solid; width:167px; height:20px;padding-left:12px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#7a7979; overflow:hidden; display:block;margin-top:18px; margin-left:-75px;}
.textbox14{ border:1px #a2b5c1 solid; width:140px; height:20px;font-family:Arial, Helvetica, sans-serif; font-size:14px; color:#7a7979; overflow:hidden; display:block;}
.textbox16{ border:1px #a2b5c1 solid; width:50px; height:20px;font-family:Arial, Helvetica, sans-serif; font-size:14px; color:#7a7979; overflow:hidden; display:block;}

.textbox5{ border:1px #a2b5c1 solid; width:179px; height:20px;padding-left:12px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#7a7979;margin-left:6px;}
.textbox6{ border:1px #d7d7d7 solid; width:189px; height:24px;padding-left:12px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#918f8f; overflow:hidden; display:block;margin-top:18px; margin-left:0px;}
.textbox7{ border:1px #d7d7d7 solid; width:189px; height:28px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#918f8f; margin-top:18px; padding:5px; margin-left:5px;}
.textbox8{ border:1px #d9d9d9 solid; width:94px; height:24px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#bbbbbb; padding:2px; margin-left:5px;}
.textbox9{ border:1px #d7d7d7 solid; width:189px; height:24px;padding-left:12px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#918f8f; overflow:hidden; display:block;margin-top:10px; margin-left:10px;}
.textbox10{ border:1px #d7d7d7 solid; width:189px; height:24px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#918f8f; padding:2px; margin-left:10px; margin-top:10px;}
.textbox11{ border:1px #d7d7d7 solid; width:189px; height:24px;padding-left:12px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#918f8f; margin-top:10px; margin-left:10px;}
.textbox12{ border:1px #d9d9d9 solid; width:94px; height:24px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#918f8f; padding:2px; margin-left:10px; margin-top:10px;}
.textbox13{ border:1px #a2b5c1 solid; width:120px; height:20px;padding-left:12px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#7a7979;margin-left:6px;}
.textbox15{ border:1px #d7d7d7 solid; width:189px; height:24px;padding-left:12px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#918f8f; margin-top:10px; margin-left:-70px;}
.txtarea{ border:1px #d7d7d7 solid; width:189px; height:55px;padding-left:12px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#918f8f; overflow:hidden; display:block;margin-top:10px; margin-left:10px; padding-top:10px;}
.submit-btn {
	background:url(../../img/submit-btn.jpg) no-repeat!important;
	width:50px;
	height:26px;
	margin-top:5px; margin-left:10px; 
	cursor:pointer;
	border:none;
}
a.sign-txt{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#7a7979; text-decoration:underline; padding-left:10px;}
a.sign-txt:hover{text-decoration:none; color:#fba325;}

.btn-search {background:url(../../img/search-btn.jpg) no-repeat!important;width:50px;height:23px;margin-top:18px; margin-left:-70px;cursor:pointer;border:none;}
.btn-searchnow {background:url(../../img/searchnow-btn.jpg) no-repeat!important;width:78px;height:26px;margin-top:18px; margin-left:5px;cursor:pointer;border:none;}
.btn-addlamps{margin-left:160px;cursor:pointer;border:none; float:left;}
.btn-rlc {background:url(../../img/rlc-btn.jpg) no-repeat!important;width:78px;height:26px;margin-top:20px; margin-left:15px;cursor:pointer;border:none;}

.btn-save {background:url(../../img/save-btn.jpg) no-repeat!important;width:50px;height:23px;margin-top:10px; margin-left:15px;cursor:pointer;border:none;}
.btn-cancel {background:url(../../img/cancel-btn.jpg) no-repeat!important;width:50px;height:23px;margin-top:10px; cursor:pointer;border:none;}

.btn-editrmu {background:url(../../img/button1.jpg) no-repeat!important;width:120px;height:40px;margin-top:10px; margin-left:15px;cursor:pointer;border:none;}

.btn-cancel1 {background:url(../../img/button3.jpg) no-repeat!important;width:120px;height:40px;margin-top:10px; cursor:pointer;border:none;}

.btn-editremark {background:url(../../img/button2.jpg) no-repeat!important;width:120px;height:40px;margin-top:10px; cursor:pointer;border:none;}

/*..........FOOTER SECTION........*/
#bg-footer{background:#dbdbdb repeat-x; width:100%; height:47px;}
.footer{width:1001px; margin:0px auto; height:47px;}
.footer1{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#535353; text-decoration:none; padding-top:15px; float:left; padding-left:5px;}
.schnell-logo{ float:right; margin-right:13px; margin-top:9px;}

#bg-footer1{background:#dbdbdb repeat-x; width:100%; height:47px;}
.footer{width:1001px; margin:0px auto; height:47px;}
.footer1{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#535353; text-decoration:none; padding-top:15px; float:left; padding-left:5px;}
.schnell-logo{ float:right; margin-right:200px; margin-top:9px;}

/*...........INNER PAGE..............*/
.menulist ul {margin:0px; padding:10px 0 0 810px; list-style:none;}
.menulist li {float:left; display:block;list-style:none; font-family:Arial, Helvetica, sans-serif; font-size:11px;color:#ffffff;background:url(../../img/line.jpg) no-repeat right 0px;line-height:10px;}
.menulist li a {font-family:Arial, Helvetica, sans-serif; font-size:11px;color:#ffffff;text-decoration:none;padding:0px 4px 0 4px;}
.menulist li a:hover { color:#fba325;}
li.act a {color:#fba325;}
li.act a:hover{color:#fba325;}
li.last{background:none;}

.welcome-head{font-family:Arial, Helvetica, sans-serif; font-size:15px; color:#60a580; padding:15px 0 0 17px; width:174px; float:left; }
.welcome-head span{ color:#fba325;}

.welcome-head1{font-family:Arial, Helvetica, sans-serif; font-size:18px; color:#ffffff;padding-top:5px; text-align:center;}
.welcome-head1 span{ color:#C2FC04;}

.list ul{margin:0px; padding:0px; list-style:none;}
.list li{float:left; list-style:none; font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#000000;}
.list li a{font-family:Arial, Helvetica, sans-serif; font-size:11px;color:#ffffff;text-decoration:none; padding:0px 5px 0 5px; float:left;}

#bg-nav{background:#9e9e9e repeat-x;height:21px; float:right; margin-top:11px; margin-right:14px;}
#bg-nav1{background:#9e9e9e repeat-x;height:21px; float:right; margin-top:6px; margin-right:14px;}
.menu ul {margin:0px; padding:0px; list-style:none;}
.menu li {float:left;list-style:none; font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#ffffff; background:url(../../img/nav-line.jpg) no-repeat right 0px;line-height:21px;}
.menu li a {font-family:Arial, Helvetica, sans-serif; font-size:11px;color:#ffffff;text-decoration:none; padding:0px 5px 0 5px; float:left;}
.menu li a:hover {background:#ff812d repeat-x center; display:block;margin-right:1px;}
li.menuact a {background:#ff812d repeat-x center; display:block;margin-right:1px;}
li.menuact a:hover{background:#ff812d repeat-x center; display:block;margin-right:1px;}
li.lastline{background:none;}

.table-head-txt1{font-family:Arial, Helvetica, sans-serif; font-size:14px; color:#000000;}
.table-head-txt{font-family:Arial, Helvetica, sans-serif; font-size:13px; color:#000000;}

.blue-box{ border:1px #95b4c6 solid; width:250px; height:70px;float:left; margin:20px 0 0 0; padding:0 0 20px 0; background-color:#f1faf7; }
.blue-box1{ border:1px #95b4c6 solid;  width:700px; height:100px;float:left; margin:20px 60px 0 150px; padding:0 0 30px 0; background-color:#f1faf7; }
.blue-box2{ border:1px #95b4c6 solid;  width:980px; height:100px;float:left; margin:20px 60px 0 10px; padding:0 0 30px 0; background-color:#f1faf7; }
.blue-box3{ border:1px #95b4c6 solid;  width:320px; height:120px;float:left; margin:200px 60px 0 350px; padding:0 0 30px 0; background-color:#f1faf7; }
.img-box{border:1px #95b4c6 solid; width:164px; height:125px; float:left; margin:20px 0 0 20px;background-color:#f1faf7;}
.grey-img{background:url(../../img/img.jpg) no-repeat; width:152px; height:112px; float:left; margin:7px 6px;}

.rgt-box{margin:20px 0 0 20px;border:1px #95b4c6 solid; width:190px; height:85px; float:left; padding-top:40px;background-color:#f1faf7;}

#bg-green{background:url(../../img/green-bg.jpg) repeat-x; width:990px;height:30px; margin:18px 0 0 6px;}

#customers
{
font-family: Arial, Helvetica, sans-serif;
width:990px;
border-collapse:collapse;

}
#customers td, #customers th 
{
font-size:11px; font-family:Arial, Helvetica, sans-serif; font-weight:normal;
border:1px #FFFFFF solid;
padding:3px 7px 2px 7px;
background-color:#ffffff;
}
#customers th 
{ font-family:Arial, Helvetica, sans-serif;
font-size:11px;
text-align:center;
padding-top:5px;
padding-bottom:4px;
background:url(../../img/green-bg.jpg) repeat-x;
color:#ffffff;
}
#customers tr.alt td 
{
color:#333333;
background-color:#e9e9e9;
}

#customers1
{
font-family: Arial, Helvetica, sans-serif;
width:787px;
border-collapse:collapse;
}

#customers1 td, #customers1 th 
{
font-size:11px; font-family:Arial, Helvetica, sans-serif; font-weight:normal;
border:1px #bbbbbb solid;
padding:3px 0 2px 0;
background-color:#ffffff;
}

#customers3 td
{
font-size:12px; font-family:Arial, Helvetica, sans-serif; font-weight:normal;
border:1px #bbbbbb solid;
padding:3px 0 2px 0;
background-color:#ffffff;
color:#494343;
}

#customers1 th 
{ font-family:Arial, Helvetica, sans-serif;
font-size:14px;
text-align:center;
padding-top:5px;
padding-bottom:4px;
background:url(../../img/green-bg.jpg) repeat-x;
color:#ffffff;
}

#customers3 th 
{ font-family:Arial, Helvetica, sans-serif;
font-size:15px;
text-align:center;
padding-top:5px;
padding-bottom:4px;
background:url(../../img/green-bg.jpg) repeat-x;
color:#ffffff;
}

#customers1 tr.alt1 td 
{
color:#333333;
background-color:#e9e9e9;
}

#customers2
{
font-family: Arial, Helvetica, sans-serif;
width:990px;
border-collapse:collapse;

}
#customers2 td, #customers2 th 
{
font-size:11px; font-family:Arial, Helvetica, sans-serif; font-weight:normal;
border:1px #bbbbbb solid;
padding:3px 7px 2px 7px;
background-color:#bbbbbb;
}
#customers2 th 
{ font-family:Arial, Helvetica, sans-serif;
font-size:11px;
text-align:center;
padding-top:5px;
padding-bottom:4px;
background:#309a69 repeat-x;
color:#ffffff;
}
#customers2 tr.alt td 
{
color:#333333;
background-color:#e9e9e9;
}

#bg-lightgreen{background:#7db597 repeat-x; width:990px;height:23px;}
.lightgreen-txt{background:url(../../img/arrow-img.jpg) no-repeat left;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#FFFFFF;  padding:0 0 0 13px;}

#bg-grey{background:#a1a1a1 repeat-x; width:990px; height:23px;}

.table-txt{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#FFFFFF; text-align:left; padding-left:10px; }
.table-txt1{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#000000;border:1px #d0d0d0 solid; height:25px;}

.no-error{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#2aa106;}
.error{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#f30202;}


.breadcrump{float:left;font-family:Arial, Helvetica, sans-serif; font-size:11px; font-weight:bold; color:#8f8f8f; height:25px; padding-top:15px; padding-left:13px;}
a.inner-link{font-family:Arial, Helvetica, sans-serif; font-size:11px; font-weight:bold; color:#8f8f8f; text-decoration:none; line-height:20px;}
a.inner-act{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#fba325; text-decoration:none; line-height:20px; font-weight:normal;}
a.inner-act:hover{color:#2f2f2f; text-decoration:underline;}

.bdr-bottom{border-bottom:1px #e3e3e3 solid; width:100%; margin-top:-9px;}
.bdr-bottom1{border-bottom:1px #e3e3e3 solid; width:2000px; margin-left:-300px;}

.lft-side{width:141px; float:left;}
.rgt-side{ width:854px; float:left;}

.quick-tab{border-left:1px #e3e3e3 solid;border-right:1px #e3e3e3 solid; width:141px; float:left; margin-top:-20px; height:400px;}
.quicklink ul{margin:0px; padding:0px; list-style:none; padding-top:15px;}
.quicklink li{margin:0px; padding:0px; list-style:none; width:134px; height:21px; border-bottom:1px #e3e3e3 solid; overflow:hidden; margin:2px 2px 2px 5px; background:url(../../img/bullet.png) no-repeat left;float:left;}
.quicklink li a{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#8f8f8f; text-decoration:none; line-height:21px; height:20px; display:block; padding-left:10px; overflow:hidden;}
.quicklink li a:hover{ background:url(../../img/bullet.png) no-repeat left  #f3f3f3;}
li.quick-act a{background:url(../../img/bullet.png) no-repeat left 6px #f3f3f3;}
li.quick-bdr{border:none;}

.grey-bg{background:url(../../img/grey-bg.jpg) repeat-x; width:823px; height:23px; float:left; margin:0 0 0 19px; font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#e8e8e8; padding:0 0 0 15px; line-height:23px;}
.grey-bg2{background:url(../../img/grey-bg.jpg) repeat-x; width:823px; height:23px; float:left; margin:0 0 0 139px; font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#e8e8e8; padding:0 0 0 15px; line-height:23px;}
.grey-bdr{border:1px #d2d2d2 solid; width:837px; float:left; margin-left:19px; margin-bottom:20px;}

.grey-bg1{background:url(../../img/grey-bg.jpg) repeat-x; width:1100px; height:23px; float:left; margin:0 0 0 -70px; font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#e8e8e8; padding:0 0 0 25px; line-height:23px;}
.grey-bdr1{border:1px #d2d2d2 solid; width:1120px; float:left; margin-left:-70px; margin-bottom:20px;}

.grey-bg2{background:url(../../img/grey-bg.jpg) repeat-x; width:835px; height:23px; float:left;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#e8e8e8; padding:0 0 0 15px; line-height:23px;}
.grey-bdr2{border:1px #d2d2d2 solid; width:850px; float:left; margin-left:139px; margin-bottom:20px; margin-top:0px;}

.search-bg{background-color:#f3f3f3; width:787px; height:80px; float:left;margin:17px 0 10px 25px;}
.search-bg1{background-color:#f3f3f3; width:900px;float:left;margin:17px 0 10px 20px; border:1px #bbbbbb solid; padding-bottom:20px;}
.search-bg2{background-color:#f3f3f3; width:830px;float:left;margin:17px 0 10px 0px; border:1px #bbbbbb solid; padding-bottom:20px;}
.lightgreen-bg{background-color:#f1faf7; width:1100px;float:left; margin:20px 0 0 10px;font-family:Arial, Helvetica, sans-serif; font-size:11px; font-weight:bold; color:#bbbbbb; text-align:center; line-height:20px; border:1px #60a580 solid;}
.lightgreen-bg1{background-color:#f1faf7; width:1100px;float:left; margin:20px 0 0 10px;font-family:Arial, Helvetica, sans-serif; font-size:11px; font-weight:bold; color:#bbbbbb; line-height:20px; border:1px #60a580 solid; height:50px; text-align:center; padding:30px 0 0 0;}

.plus-icon{background:url(../../img/plus-icon.jpg) no-repeat left 2px; font-family:Arial, Helvetica, sans-serif; font-size:11px; font-weight:bold; color:#bbbbbb; padding:0 0 0 12px; float:left;}

a.login-txt{font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#918f8f; float:left; text-decoration:none;padding-left:26px; margin-top:20px;}

.ast{color:#ff0000; padding-top:5px; font-size:14px;}

/*...........DROPDOWN MENU..............*/
        #menudiv, ul, li
        {
           margin:0px; padding:0px; list-style:none;
        }
        #menudiv
        {
            margin:0px; padding:0px; list-style:none;
        }
        #menu
        {
            list-style: none;margin:0px; padding:0px;
            background:#9e9e9e repeat-x;height:21px; float:right; margin-top:11px; margin-right:14px;
            
        }
        #menu li
        {
            float:left;list-style:none; font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#ffffff; background:url(../../img/nav-line.jpg) no-repeat right 0px;line-height:21px;
        }
        #menu li:hover
        {
            /* Background color and gradients */
        }
        #menu li a
        {
            font-family:Arial, Helvetica, sans-serif; font-size:11px;color:#ffffff;text-decoration:none; padding:0px 5px 0 5px; float:left;
        }
        #menu li a span
        {
           font-family:Arial, Helvetica, sans-serif; font-size:11px;color:#ffffff;text-decoration:none; padding:0px 5px 0 5px; float:right;
        }
        #menu li a:hover
        {
           background:#ff812d repeat-x center; display:block;margin-right:1px;
        }
        #menu li a:hover span
        {
           background:#ff812d repeat-x center; display:block;margin-right:1px;
        }
        #menu li:hover a
        {
            color: #161616;background:none; 
        }
		 #menu #non li:hover a
        {
            color: #161616; background:none;
        }
        #menu li .drop
        {
            padding-right: 10px;
        }
		
        #menu li:hover .drop
        { background:#ff812d repeat-x center; display:block;margin-right:1px;
        }
		
        .dropdown_1column, .dropdown_2columns, .dropdown_3columns, .dropdown_4columns, .dropdown_5columns
        {
            margin: 10px auto;
            float: left;
            position: absolute;
            left: -999em; /* Hides the drop down */
            text-align: left;
            padding: 10px 5px 10px 5px;
        }
		.dropdown_10column
        {
            margin: 10px auto;
            float: left;
            position: absolute;
            left: -999em; /* Hides the drop down */
            text-align: left;
            padding: 10px 5px 10px 5px;
        }
        .dropdown_1column
        {
            width: 840px;
        }
        .dropdown_2columns
        {
            width: 280px;
        }
        .dropdown_3columns
        {
            width: 420px;
        }
        .dropdown_4columns
        {
            width: 560px;
        }
        .dropdown_5columns
        {
            width: 700px;
        }
        #menu li:hover .dropdown_1column, #menu li:hover .dropdown_2columns, #menu li:hover .dropdown_3columns, #menu li:hover .dropdown_4columns, #menu li:hover .dropdown_5columns
        {
            left: -1px;
            top: auto;
        }
       .col_1, .col_2, .col_3, .col_4, .col_5
        {
            display: inline;
            float: right;
            position: relative;
            margin-left: 20px;
            margin-right: 200px;
        }
		.col_10
        {
            display: inline;
            float:left;
            position: relative;
            margin-left: 5px;
            margin-right: 5px; padding-right:200px;
        }
        #menu .menu_right
        {
            float: right;
            margin-right: 0px;
        }
        #menu li .align_right
        {
        }
        #menu li:hover .align_right
        {
            left: auto;
            right: -1px;
            top: auto;
        }
        #menu p, #menu h2, #menu h3, #menu ul li
        {
            line-height: 21px;
            f: ttext-align:left;
            text-shadow: 1px1px1px#FFFFFF;
        }
        #menu h2
        {
            font-size: 21px;
            font-weight: 400;
            letter-spacing: -1px;
            margin: 7px 0 14px 0;
            padding-bottom: 14px;
            border-bottom: 1px solid #666666;
        }
        #menu h3
        {
            font-size: 14px;
            margin: 7px 0 14px 0;
            padding-bottom: 7px;
            border-bottom: 1px solid #888888;
        }
        #menu p
        {
            line-height: 18px;
            margin: 0 0 10px 0;
        }
        #menu li:hover div a
        {
            color: #015b86;
        }
        #menu li:hover div a:hover
        {
            color: #029feb;
        }
        .strong
        {
            font-weight: bold;
        }
        .italic
        {
            font-style: italic;
        }
        .imgshadow
        {
            /* Better style on light background */
            background: #FFFFFF;
            padding: 4px;
            border: 1px solid #777777;
            margin-top: 5px;
        }
        .img_left
        {
            /* Image sticks to the left */
            width: auto;
            float: left;
            margin: 5px 15px 5px 5px;
        }
        #menu li .black_box
        {
            background-color: #333333;
            color: #eeeeee;
            text-shadow: 1px 1px 1px #000;
            padding: 4px 6px 4px 6px;
        }
        #menu li ul
        {
            list-style: none;
            padding: 0;
            margin: 0 30px 12px 0;
        }
        #menu li ul li
        {
            font-size: 12px; color:#000000;
            line-height: 21px;
            position: relative;
            
            padding: 0;
            margin: 0;
            float: left;
            text-align: left;
            padding: 0px 3px 0px 3px;
            padding: 0px 0px 0px 3px;
        }
        #menu li ul li a
        {
            background: none;
            padding: 0px 0px 0px 3px;
            color: #0059B1;
            font-weight: bold;
            font: bold 11px ;Arial,Helvetica,sans-serif;
            line-height: 21px;
        }
        #menu li ul li:hover
        {
            background: none;
            border: none;
            padding: 0;
            margin: 0;
            padding: 0px 0px 0px 3px;
            color: #0059B1;
        }
        #menu li .greybox li
        {
            background: #F4F4F4;
            border: 1px solid #bbbbbb;
            margin: 0px 0px 4px 0px;
            padding: 4px 6px 4px 6px;
            width: 116px;
        }
        #menu li .greybox li:hover
        {
            background: #ffffff;
            border: 1px solid #aaaaaa;
            padding: 4px 6px 4px 6px;
            margin: 0px 0px 4px 0px;
        }
        .displayConfig
        {
            background-position: 0 -82px;
        }
        .hoverclr
        {
            background-position: 0 -82px !important;
        }

.tab_content {
	padding: 20px;
	font-size: 1.2em;
}
 ul.tabs {
margin: 0 0 0 50px;padding:0;float: left;list-style: none;height: 23px;width: 100%;}
ul.tabs li {
float: left;
	margin: 0;
	padding: 0;
	height: 23px;font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#7a7979;font-weight:bold;
	line-height: 23px;
	border:1px #309a69 solid;
	margin-bottom: 0px; background-color:#f3f3f3;
	overflow: hidden;
	position: relative;margin-right:30px;
}
ul.tabs li a {
text-decoration: none;
	font-family:Arial, Helvetica, sans-serif; font-size:11px; color:#7a7979;font-weight:bold;
	display: block;
	font-size: 0.9em;
	padding: 0 10px 0 10px;
	border:1px #309a69 solid;
	outline: none;text-align:center;

}
ul.tabs li a:hover {
	color:#ff812d!important;display:block; background-color:#e9f7f0;
}
li.last{background:none; }	
html ul.tabs li.active, html ul.tabs li.active a:hover  {
	 color:#ff812d!important;display:block;background-color:#e9f7f0;
}

a.load-map {
	background:#5AB89C;
	width:90px;
	height:30px;
	font-family: Arial, Helvetica, sans-serif;
	font-size:12px;
	color:#ffffff; line-height:30px;
	font-weight:600; text-align:center;
	border:none;
	cursor:pointer;
	text-transform:uppercase;
	text-decoration:none;
	margin-top:5px; float:left; 
}
a.load-map:hover {
	background:#478E79;	
	text-decoration:none;
}
