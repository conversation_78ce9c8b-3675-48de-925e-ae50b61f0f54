<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>Welcome to Schnell</title>
<link rel="stylesheet" type="text/css" href="../../../../../../site_media/css/styles/style.css" />
<link rel="stylesheet" href="../../../../../../site_media/js/jquery_ui.css" />
<script src="../../../../../../site_media/js/jquery.js"></script>
<script src="../../../../../../site_media/js/jquery_ui.js"></script>
<script src="../../../../../../site_media/js/reportselect/report_selection.js"></script>
</head>
<script>
$(function() {
    $( "#datepicker" ).datepicker({
        dateFormat: "yy-mm-dd",
    });
});
$(function() {
    $( "#datepicker1" ).datepicker({
        dateFormat: "yy-mm-dd",
    });
});
$(function(){    
    $(document).ready(function(){	
	var url=window.location.href;
	url = url.substring(7)
	window.urlpath = url.substring(0,url.indexOf('/'));
	$('#datepicker').datepicker('setDate', '-1');	   
	$('#datepicker1').datepicker('setDate', '+0');		  
       });
});
</script>
<body>

<!--Top section starts here-->
  <div id="green-top">
    <div class="top-green">        
    <div class="welcome-head1" Align="center">ILM DATA </div>             
     </div>
  </div>
</div>

<div class="whitecontent-bg">
 <div class = "blue-box1" align="center">
      <table width="600"  cellpadding="5" align="center" cellspacing="5" border="0" >				
		<tr><td></td></tr>
		<tr><td></td></tr>
		<tr>
		<td class="txtname7">From Date : </td>
		<td class ="txtname2"><input type="text" id="datepicker"  readonly="True"></td>
		<td class="txtname7">Filter by Text : </td>
		<td><input type="text" value="" class="textbox14" name="" id="search_txt" maxlength=16></td>
		</tr>
		<tr>
		<td class="txtname7">To Date :  </td>
		<td class ="txtname2"><input type="text" id="datepicker1" readonly="True"></td>					
		<td> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<button id="create-user1">View Report</button></td>
		<td><button id="create-user2">View ALL</button></td>		
		</tr>	
	</table>
    </div>

<br clear="all" />  
<br clear="all" />    
<div class="bdr-bottom1"></div>
<br clear="all" />
</br>
<!--footer section ends here-->
</body>
</html>
