function setCookie(cname,cvalue,exdays)
{
	var d = new Date();
	d.setTime(d.getTime()+(exdays*24*60*60*1000));	
	var expires = "expires="+d.toGMTString();	
	document.cookie = cname + "=" + cvalue + "; " + expires + ";  path=/";
}

function getCookie(cname)
{
	var name = cname + "=";
	var ca = document.cookie.split(';');	
	for(var i=0; i<ca.length; i++) 
	{
		var c = ca[i].trim();
		if (c.indexOf(name)==0) return c.substring(name.length,c.length);
	}
	return "";
}

function checkCookie()
{
		var user=getCookie("username");
		if (user!="")
		{
			alert("Welcome again " + user);
		}
		else 
		{
			user = prompt("Please enter your name:","");
			if (user!="" && user!=null)
			{
				setCookie("username",user,365);
			}
		}
}

function deleteCookie(cname)
{	//Sat, 05 Apr 2014 07:36:45 GMT
	document.cookie = cname +"=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/";
}
