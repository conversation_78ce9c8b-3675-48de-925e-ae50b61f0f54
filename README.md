### Schnell-IoT (schnelliot-api) Custom API Services

#### Project Environment
    Ubuntu 24.04 LTS
    Python 3.12
    Web Server: Apache2 - look ./conf for configuration

#### .env variables
    # Django Secret
    SECRET_KEY=

    # Log configuration
    LOG_FILE_NAME=/var/log/schnelliot/iotpro-api.log
    
    # Db Configuration
    HOST=localhost
    USER=
    DBNAME=
    PASSWORD=
    PORT=
    
    # Thingsboard Configuration
    BASE_URL=http://localhost:8080
    TB_USERNAME=
    TB_PASSWORD=
    
    # AWS Configuraiton
    AWS_BUCKET_TYPE=s3
    AWS_BUCKET_NAME=
    AWS_BUCKET_REGION=
    AWS_BUCKET_ACCESS_KEY=
    AWS_BUCKET_SECRET_KEY=
    AWS_BUCKET_FOLDER_NAME=luminator
    AWS_BUCKET_LUMINATOR_FOLDER_NAME=luminator
    AWS_BUCKET_GRIEVANCE_FOLDER_NAME=grievances
    AWS_INSTALL_SERVICE_SQS_URL=
    AWS_INSTALL_SERVICE_REGION=
    AWS_INSTALL_SERVICE_ACCESS_KEY=
    AWS_INSTALL_SERVICE_SECRET_KEY=
    
    # Google Project information
    SERVICE_ACCOUNT_KEY_PATH=/opt/.credentials/gcp-credentials-<variant>.json
    
    PUBSUB_POLE_TOPIC_PATH=
    PUBSUB_LAMP_TOPIC_PATH=
    PUBSUB_ACTIVITY_LOG_TOPIC_PATH=


Deployment Directory: 

    /opt/deployment/schnelliot-api
Credentials Directory:

    /opt/.credentials/
   

Production Log files:

    tail -f /var/log/schnelliot/schnelliot-api.log
    tail -f /var/log/apache2/schnelliot-api-error.log

Staging Log files:

    tail -f /var/log/schnelliot/iotpro-api.log
    tail -f /var/log/apache2/iotpro-api-error.log


