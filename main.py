# Hide SQLAlchemy warning
import warnings

import os
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse

from tb_service.lume.routes import router as lume_router
from tb_service.onboarder.api import router as onboarder_router
from tb_service.sms_tool.routes import router as sms_router

app = FastAPI()

# Serve static files from /site_media/
site_media = os.path.join(os.path.dirname(__file__), "site_media")
app.mount("/site_media", StaticFiles(directory="site_media"), name="static")


app.include_router(lume_router, prefix='/api')
app.include_router(onboarder_router, prefix='/onboard')
app.include_router(sms_router, prefix='/sms')


@app.get("/")
async def home():
    return JSONResponse({'status':200, 'message': 'Welcome to SCHENLL IOT API'})

