from fastapi import <PERSON><PERSON><PERSON>, Request, Form
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from tb_service.config import TEMPLATE_DIR as templates
import urllib.parse
import urllib.request
import datetime
import socket


def msg_format(phoneno, message):
    _sms_url = "https://api.instaalerts.zone/SendSMS/sendmsg.php"
    if len(phoneno) == 10:
        senturl = _sms_url + '?uname=ppptest&pass=Sch@Air2&send=SCHNEL&'
    else:
        senturl = _sms_url + '?uname=sl1-ops&pass=SL1V@r~V)d3&send=SCHNEL&'
    senturl += 'dest=91' + phoneno + '&msg=' + message + '%2DSEEPL'
    print(senturl)
    return senturl


async def login(request: Request):
    return templates.TemplateResponse("reports/smstool.html", {"request": request})


async def send_sms(request: Request):
    form = await request.form()
    data = urllib.parse.urlencode(form)
    datalist = data[:-1].split("%7E")
    Phonenolist = datalist[0].split("%2C")
    result = 'Result : '

    for phone in Phonenolist:
        try:
            if len(phone) == 10 or len(phone) == 13:
                url = msg_format(phone.replace(' ', ''), datalist[1])
                response = urllib.request.urlopen(url, timeout=20)
                result += f"{phone} - S , "
            else:
                result += f"{phone} - IV , "
        except urllib.error.HTTPError:
            result += f"{phone} - HF , "
        except urllib.error.URLError:
            result += f"{phone} - UF , "
        except socket.timeout:
            result += f"{phone} - TO , "

    redate = datetime.datetime.now().strftime('%H:%M')
    return JSONResponse(content=result[:-2] + ' Time <>  ' + redate)


async def gfs_rmu_url_redirect(sim_no: str):
    result = 'Result : '
    if sim_no:
        try:
            if len(sim_no) == 10 or len(sim_no) == 13:
                encoded_msg = urllib.parse.quote_plus("*SIP r.schnelliot.in,1#")
                url = msg_format(sim_no, encoded_msg)
                response = urllib.request.urlopen(url, timeout=20)
                result += f"{sim_no} - S , "
            else:
                result += f"{sim_no} - IV , "
        except urllib.error.HTTPError:
            result += f"{sim_no} - HF , "
        except urllib.error.URLError:
            result += f"{sim_no} - UF , "
        except socket.timeout:
            result += f"{sim_no} - TO , "

    redate = datetime.datetime.now().strftime('%H:%M')
    return JSONResponse(content=result[:-2] + ' Time <>  ' + redate)
