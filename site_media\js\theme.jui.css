/*************
  jQuery UI Theme
 *************/
 /* overall */
.tablesorter-jui {
	width: 100%;
	border-collapse: separate;
	border-spacing: 2px; /* adjust spacing between table cells */ 
	margin: 10px 0 15px;
	padding: 5px;
	font-size: 0.8em;
}

/* header */
.tablesorter-jui th,
.tablesorter-jui thead td {
	position: relative;
	background-repeat: no-repeat;
	background-position: right center;
	/* UI hover and active states make the font normal and the table resizes, this fixes it */
	font-weight: bold !important;
	border-width: 1px !important;
	text-align: left;
}
.tablesorter-jui .header,
.tablesorter-jui .tablesorter-header {
	cursor: pointer;
	white-space: normal;
	padding: 8px; /* wider than the icon */
}
.tablesorter-jui .tablesorter-header-inner {
	padding-right: 20px;
}
.tablesorter-jui thead tr th .ui-icon {
	position: absolute;
	right: 3px;
	top: 50%;
	margin-top: -8px; /* half the icon height; older IE doesn't like this */
}

/* tfoot */
.tablesorter-jui tfoot th,
.tablesorter-jui tfoot td {
	font-weight: normal !important;
	font-size: .9em;
	padding: 2px;
}

/* tbody */
.tablesorter-jui td {
	padding: 4px;
	vertical-align: top;
}

/* hovered row colors */
.tablesorter-jui tbody > tr:hover td {
	opacity: 0.7;
	filter: alpha(opacity=70);
}

/* table processing indicator */
.tablesorter-jui .tablesorter-processing .tablesorter-header-inner {
	background-position: center center !important;
	background-repeat: no-repeat !important;
	/* background-image: url(../addons/pager/icons/loading.gif) !important; */
	background-image: url('data:image/gif;base64,R0lGODlhFAAUAKEAAO7u7lpaWgAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQBCgACACwAAAAAFAAUAAACQZRvoIDtu1wLQUAlqKTVxqwhXIiBnDg6Y4eyx4lKW5XK7wrLeK3vbq8J2W4T4e1nMhpWrZCTt3xKZ8kgsggdJmUFACH5BAEKAAIALAcAAAALAAcAAAIUVB6ii7jajgCAuUmtovxtXnmdUAAAIfkEAQoAAgAsDQACAAcACwAAAhRUIpmHy/3gUVQAQO9NetuugCFWAAAh+QQBCgACACwNAAcABwALAAACE5QVcZjKbVo6ck2AF95m5/6BSwEAIfkEAQoAAgAsBwANAAsABwAAAhOUH3kr6QaAcSrGWe1VQl+mMUIBACH5BAEKAAIALAIADQALAAcAAAIUlICmh7ncTAgqijkruDiv7n2YUAAAIfkEAQoAAgAsAAAHAAcACwAAAhQUIGmHyedehIoqFXLKfPOAaZdWAAAh+QQFCgACACwAAAIABwALAAACFJQFcJiXb15zLYRl7cla8OtlGGgUADs=') !important;
}

/* Zebra widget - This allows you to use ui-state-default as the zebra stripe color */
.tablesorter-jui tr.ui-state-default {
	background-image: url();
	font-weight: normal;
}

/* processing background color */
.tablesorter-jui .tablesorter-processing {
	background-color: #ddd; /* older browsers that don't support rgba */
	background-color: rgba(255,255,255,0.8);
}

/* filter widget */
.tablesorter-jui .tablesorter-filter-row td {
	background: transparent;
	line-height: normal;
	text-align: center; /* center the input */
	-webkit-transition: line-height 0.1s ease;
	-moz-transition: line-height 0.1s ease;
	-o-transition: line-height 0.1s ease;
	transition: line-height 0.1s ease;
}
/* optional disabled input styling */
.tablesorter-jui .tablesorter-filter-row .disabled {
	opacity: 0.5;
	filter: alpha(opacity=50);
	cursor: not-allowed;
}
/* hidden filter row */
.tablesorter-jui .tablesorter-filter-row.hideme td {
	/*** *********************************************** ***/
	/*** change this padding to modify the thickness     ***/
	/*** of the closed filter row (height = padding x 2) ***/
	padding: 2px;
	/*** *********************************************** ***/
	margin: 0;
	line-height: 0;
	cursor: pointer;
}
.tablesorter-jui .tablesorter-filter-row.hideme .tablesorter-filter {
	height: 1px;
	min-height: 0;
	border: 0;
	padding: 0;
	margin: 0;
	/* don't use visibility: hidden because it disables tabbing */
	opacity: 0;
	filter: alpha(opacity=0);
}
/* filters */
.tablesorter-jui .tablesorter-filter {
	width: 98%;
	height: inherit;
	margin: 0;
	padding: 4px;
	background-color: #fff;
	border: 1px solid #bbb;
	color: #333;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-transition: height 0.1s ease;
	-moz-transition: height 0.1s ease;
	-o-transition: height 0.1s ease;
	transition: height 0.1s ease;
}
