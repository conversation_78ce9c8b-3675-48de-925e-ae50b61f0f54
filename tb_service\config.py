import json
import logging.config
import os
import sys
import logging
import pymysql
import requests
from requests.adapters import HTTPAdapter
from fastapi.templating import Jinja2Templates
from urllib3.util.retry import Retry
from dotenv import load_dotenv
from pathlib import Path


pymysql.install_as_MySQLdb()

# Initialise environment variables
load_dotenv()

HEADER = {'Content-type': 'application/json', 'Accept': '*/*'}

LOGIN_CREDENTIALS = json.dumps({"username": os.environ['TB_USERNAME'], "password": os.environ['TB_PASSWORD']})

session = requests.Session()
retry = Retry(total=5, connect=5, backoff_factor=0.5, status_forcelist=[500, 502, 503, 504])
adapter = HTTPAdapter(max_retries=retry, pool_connections=100, pool_maxsize=100)
session.mount('http://', adapter)
session.mount('https://', adapter)

BASE_DIR = Path(__file__).resolve().parent.parent

MEDIA_URL = '/'
# CSML Data forwading URLs
CSML_LOGIN_URL = "https://bi.smartkochi.in:8443/auth/realms/schnellenergy/protocol/openid-connect/token"
CSML_LAMP_FAILURE_URL = "https://bi.smartkochi.in:8443/apiman-gateway/FLUENTGRID/Lampfailure/1.0?apikey=1c7b2899-167f-443b-8b33-64885e6cac4a"
# lamp_fail_url = "https://bi.smartkochi.in:8443/ccc/lampFailureData"
kochin_user_data = "username=SchnellEnergyUser&password=sch%24ell@n@r%26yu%24er&grant_type=password&client_id=SchnellEnergy"

BASE_URL = os.environ.get('BASE_URL')
ILM_SERVER_ATTRIBUTES = "testResults,PDI,state,condition,dimmable,active,wardName,zoneName,region,qrCount"
LUME_PRINT_QR_REQUIRED_ROLE = "production manager"
ILM_ATTRIBUTE_TYPE_CONVERT_BOOL = ["dimmable", "PDI", "brightness_100", "brightness_70", "brightness_50",
                                   "brightness_30", "brightness_0", "flash", "rtc", "active"]
ILM_ATTRIBUTE_TYPE_CONVERT_INT = ["qrCount", "error_count", "start_ts", "end_ts", "lastActivityTime", "installedOn",
                                  "lampWatts", "armCount", "lampWattage"]
ILM_ATTRIBUTE_TYPE_CONVERT_DOUBLE = ["latitude", "longitude", "accuracy", "slatitude", "slongitude"]
GW_SERVER_ATTRIBUTES = "active,state,condition,wardName,zoneName,region"
ENTITY_TYPE = ["DEVICE", "ASSET"]
ENTITY_TYPE_ASSET = "ASSET"
ENTITY_TYPE_DEVICE = "DEVICE"
ATTRIBUTE_SCOPES = ["CLIENT_SCOPE", "SERVER_SCOPE", "SHARED_SCOPE"]
DEVICE_TYPES = ["gw", "ilm", "ilm-4g", "ccms"]
RELATION_TYPES = ["Contains", "ControlledBy", "LitBy", "Powers", "Routes", "Mounts", "CanAccess"]
ILM_TEST_QUERY_PARAMS = ["jigNumber", "deviceId", "action"]
GW_ENTITY_ID_KEY = ["deviceId"]
ENTITY_RELATIONS = ["FROM", "TO"]
GW_DISPATCH_QUERY_PARAMS = ["panelId", "gwType", "ebMeterNo", "phase", "simNo"]
GW_ASSET_TYPES = ["ccms", "hub"]
ILM_ASSET_TYPES = ["lamp", "lightPoint", "region", "zone", "ward", "pole"]
LIGHT_POINT_INSTALLATION_ATTRIBUTES = ["latitude", "longitude", "accuracy", "landmark", "wardName", "zoneName",
                                       "region", "installedOn", "installedBy", "lampWatts"]
ILM_INSTALLATION_ATTRIBUTES = ["state", "condition", "landmark", "latitude", "longitude", "zoneName", "region",
                               "wardName", "installedOn", "installedBy"]
CCMS_INSTALLATION_ATTRIBUTES = ["slatitude", "slongitude", "accuracy", "location", "wardName", "zoneName", "region",
                                "installedOn", "installedBy"]
CCMS_EBMETER_ATTRIBUTES = {"ebMeterNo": "name", "meterReadingOffset": "meterReadingOffset",
                           "ebMeterReplacedBy": "replacedBy", "ebMeterReplacedOn": "replacedOn"}
GW_INSTALLATION_ATTRIBUTES = ["state", "condition", "location", "wardName", "zoneName", "region", "installedOn",
                              "installedBy"]
POLE_INSTALLATION_ATTRIBUTES = ["type", "lampProfiles", "latitude", "longitude", "accuracy", "location", "wardName",
                                "zoneName", "region", "state", "installedOn", "installedBy", "connection", "armCount",
                                "clampDimension"]
LAMP_INSTALLATION_ATTRIBUTES = ["lampWatts", "manufacturer", "lampType", "year", "wardName", "installedOn",
                                "installedBy", "dimmable", "state", "condition"]
DEVICE_FAILURE_STATUS_HANDLER = [200, 401]
COMMON_PARAMS = "lastActivityTime"
LIGHT_POINT_ASSET_FORMAT = "LP-{asset_name}"
LAMP_ASSET_FORMAT = "LMP-{asset_name}"
AVAILABLE_STATES = ["TESTABLE", "TESTED", "INSTALLABLE", "INSTALLED"]
AVAILABLE_CONDITION = ["NEW", "SERVICE", "SCRAPPED"]
REPLACE_AND_REMOVE_DEVICE_STATE_AND_CONDITION = {"state": "INSTALLABLE"}
LAMP_INSTALLED_STATE_CONDITION = {"state": "INSTALLED", "condition": "NEW"}
INSTALLED_STATE = {"state": "INSTALLED"}
OWNER_TYPE = ["CUSTOMER", "TENANT", "USER"]
OWNER_TYPE_CUSTOMER = "CUSTOMER"
LP_SERVER_ATTRIBUTES = ["accuracy", "lamp"]
DEVICE_FIND_QUERY_ATTRIBUTE_RESPONSE_KEYS = ["state", "condition", "region", "wardName", "zoneName", "location",
                                             "landmark", "latitude", "longitude", "slatitude", "slongitude",
                                             "commissioned", "lastActivityTime", "active", "lampWatts", "accuracy",
                                             "installedOn", "installedBy"]
DEVICE_FIND_QUERY_ENTITY_RESPONSE_KEYS = {"name": "name", "label": "label", "type": "type", "device_id": "id"}
PROJECT_WISE_WATTAGE = "lampWattage"

# mysql db for device onboard
DATABASES_NAME = os.environ.get('DB_NAME')
DATABASES_USER = os.environ.get('DB_USER')
DATABASES_PASSWORD = os.environ.get('DB_PASSWORD')
DATABASES_HOST = os.environ.get('DB_HOST')
DATABASES_PORT = os.environ.get('DB_PORT')

AWS_BUCKET_TYPE = os.environ.get('AWS_BUCKET_TYPE')
AWS_BUCKET_NAME = os.environ.get('AWS_BUCKET_NAME')
AWS_BUCKET_REGION = os.environ.get('AWS_BUCKET_REGION')
AWS_BUCKET_ACCESS_KEY = os.environ.get('AWS_BUCKET_ACCESS_KEY')
AWS_BUCKET_SECRET_KEY = os.environ.get('AWS_BUCKET_SECRET_KEY')
AWS_BUCKET_GRIEVANCE_FOLDER_NAME = os.environ.get('AWS_BUCKET_GRIEVANCE_FOLDER_NAME')
AWS_BUCKET_LUMINATOR_FOLDER_NAME = os.environ.get('AWS_BUCKET_LUMINATOR_FOLDER_NAME')
AWS_FILE_PATH = 'http://' + (AWS_BUCKET_NAME or '') + '.' + (AWS_BUCKET_TYPE or '') + '.' + (AWS_BUCKET_REGION or '') + '.' + 'amazonaws.com'
AWS_BUCKET_PPE_IMAGE_FOLDER_NAME = os.environ.get('AWS_BUCKET_PPE_IMAGE_FOLDER_NAME')

# GCP Integration Table details
SERVICE_ACCOUNT_KEY_PATH = os.environ.get('SERVICE_ACCOUNT_KEY_PATH')
PUBSUB_POLE_TOPIC_PATH = os.environ.get('PUBSUB_POLE_TOPIC_PATH')
PUBSUB_LAMP_TOPIC_PATH = os.environ.get('PUBSUB_LAMP_TOPIC_PATH')
PUBSUB_POLE_RELOCATION_TOPIC_PATH = os.environ.get('PUBSUB_POLE_RELOCATION_TOPIC_PATH')
PUBSUB_ACTIVITY_LOG_TOPIC_PATH = os.environ.get('PUBSUB_ACTIVITY_LOG_TOPIC_PATH')

# ILM device install service aws credentials in Testing os.environ.get
AWS_INSTALL_SERVICE_SQS_URL = os.environ.get('AWS_INSTALL_SERVICE_SQS_URL')
AWS_INSTALL_SERVICE_REGION = os.environ.get('AWS_INSTALL_SERVICE_REGION')
AWS_INSTALL_SERVICE_ACCESS_KEY = os.environ.get('AWS_INSTALL_SERVICE_ACCESS_KEY')
AWS_INSTALL_SERVICE_SECRET_KEY = os.environ.get('AWS_INSTALL_SERVICE_SECRET_KEY')

# Logging configurations

LOG_LEVEL = os.environ.get('LOGLEVEL', 'info').upper()
print("Log level set to: ", LOG_LEVEL)
LOG_FILE_NAME = os.environ.get('LOG_FILE_NAME')
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '[%(levelname)s] %(asctime)s <%(name)s.%(module)s: %(lineno)s>    %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S'
        },
        'simple': {
            'format': '%(levelname)s %(message)s'
        }
    },

    'handlers': {
        'file': {
            'level': LOG_LEVEL,
            'class': 'logging.handlers.TimedRotatingFileHandler',
            'filename': LOG_FILE_NAME,
            'when': 'W0',
            'backupCount': 100,
            'formatter': 'verbose',
        },
        'console': {
            'level': LOG_LEVEL,
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
            'stream': sys.stdout
        }
    },
    'root': {
        'handlers': ['file'],
        'level': LOG_LEVEL,
    }
}
logging.config.dictConfig(LOGGING)
logger = logging.getLogger(__name__)
logger.info("Logging configuration loaded successfully.")

CORS_ALLOWED_ORIGINS = [
    "http://iotpro.io",
    "https://iotpro.io",
    "http://schnelliot.in",
    "https://schnelliot.in",
    "http://tbce.iotpro.io:8080",
    "http://prod.schnelliot.in",
    "https://prod.schnelliot.in",
    "http://prod.schnelliot.in:8080",
    "http://192.168.1.206:8080",
    "https://signify.iotpro.io",
    "https://customergrievance-9acd9.web.app"
]
CORS_ORIGIN_WHITELIST = [
    'http://iotpro.io',
    'https://iotpro.io',
    'http://schnelliot.in',
    'https://schnelliot.in',
    "http://tbce.iotpro.io:8080",
    "http://prod.schnelliot.in",
    "https://prod.schnelliot.in",
    "http://prod.schnelliot.in:8080",
    "http://192.168.1.206:8080",
    "https://signify.iotpro.io",
    "https://customergrievance-9acd9.web.app"
]
CORS_ALLOW_METHODS = ['DELETE', 'GET', 'OPTIONS', 'PATCH', 'POST', 'PUT']
CORS_ALLOW_HEADERS = [
    'accept', 'accept-encoding', 'authorization',
    'content-type', 'dnt', 'origin', 'token',
    'user-agent', 'x-csrftoken', 'x-requested-with',
    'Accept-Language', 'Content-Language', 'X-Custom-Header'
]


LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Kolkata'

USE_I18N = True

USE_L10N = True

USE_TZ = True

STATIC_URL = '/static/'
MEDIA_ROOT = os.path.abspath('Media')
MEDIA_URL = 'Media/'


GREYTHR_HOST =  os.environ.get('GREYTHR_HOST')
GREYTHR_API_ID = os.environ.get('GREYTHR_API_ID')
GREYTHR_API_ENDPOINT = os.environ.get('GREYTHR_API_ENDPOINT')
GREYTHR_PEM_FILEPATH = os.environ.get('GREYTHR_PEM_FILEPATH')
DATA_UPLOAD_MAX_MEMORY_SIZE = 10 * 1024 * 1024 # 10 MB


TEMPLATE_DIR = Jinja2Templates(directory="templates")