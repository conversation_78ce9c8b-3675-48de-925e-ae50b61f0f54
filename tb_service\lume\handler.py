import json
import inspect
import requests
from functools import wraps
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from tb_service.config import *

class HttpResponse(JSONResponse):
    def __init__(self, content="", content_type="application/json", status_code=200):
        content = json.loads(content) if isinstance(content, str) else content
        super().__init__(content=content, media_type=content_type, status_code=status_code)

class RequestResponseHandler:
    def __init__(self):
        self.response = {"status": 200}

    def update_service_response(self, response) -> None:
        self.response = response

    def get_service_response(self) -> json:
        return json.dumps(self.response)

    def update_error_response(self, error: str) -> None:
        previous_response = json.loads(self.get_service_response())
        if previous_response.get("status") == 200:
            self.update_service_response({"status": 500, "message": error})

    @staticmethod
    def post_request(url: str, header: dict, data: str, instance: object) -> list or dict:
        try:
            logger.debug(url)
            get_post_response = requests.post(url=url, headers=header, data=data, timeout=30)
            response = RequestResponseHandler.is_success(get_post_response)
            instance.update_service_response(response)
            return response
        except Exception as e:
            logger.exception("post method failed %s" % str(e))
        return None

    @staticmethod
    def get_request(url: str, header: dict, data: str, instance: object) -> list or dict:
        try:
            logger.debug(url)
            get_response = requests.get(url=url, headers=header, data=data, timeout=30)
            response = RequestResponseHandler.is_success(get_response)
            instance.update_service_response(response)
            return response
        except Exception as e:
            logger.exception("get method failed %s" % str(e))
        return None

    @staticmethod
    def delete_request(url: str, header: dict, data: str, instance: object) -> list or dict:
        try:
            get_delete_response = requests.delete(url=url, headers=header, data=data, timeout=30)
            response = RequestResponseHandler.is_success(get_delete_response)
            instance.update_service_response(response)
            return response
        except Exception as e:
            logger.exception("delete method failed %s" % str(e))
        return None

    @staticmethod
    def is_success(response: dict or str) -> list or dict:
        success_response = {}
        try:
            if response.status_code == 403:
                success_response = {"status": response.status_code}
            success_response = json.loads(response.text)
        except Exception as e:
            success_response = {"status": 200}
        return success_response


def token_validator(func):
    @wraps(func)
    async def validate(request: Request, *args, **kwargs):
        if "token" in request.headers:
            return await func(request, *args, **kwargs)
        else:
            return JSONResponse(status_code=400, content={
                "status": 1000, "message": "Token not found"
            })

    validate.__signature__ = inspect.signature(func)
    return validate


def validate_keys(required_keys):
    def key_validation(func):
        @wraps(func)
        async def validate(request: Request, *args, **kwargs):
            body = await request.body()
            try:
                request_params = json.loads(body.decode())
            except json.JSONDecodeError:
                return JSONResponse(status_code=400, content={
                    "status": 1000, "message": "Invalid JSON"
                })

            missing_keys = [key for key in required_keys if key not in request_params]
            if missing_keys:
                return JSONResponse(status_code=400, content={
                    "status": 1000,
                    "message": f"Missing keys {', '.join(missing_keys)}"
                })
            return await func(request, *args, **kwargs)

        validate.__signature__ = inspect.signature(func)
        return validate
    return key_validation


def method_validate(required_method):
    def method_validator(func):
        @wraps(func)
        async def validate(request: Request, *args, **kwargs):
            if request.method != required_method:
                return JSONResponse(status_code=405, content={
                    "status": 405,
                    "message": "The request method is not allowed"
                })
            return await func(request, *args, **kwargs)

        validate.__signature__ = inspect.signature(func)
        return validate
    return method_validator


def request_parameter_validate():
    def method_validator(func):
        @wraps(func)
        async def validate(request: Request, *args, **kwargs):
            response = {"status": 400, "message": "Invalid request body"}
            try:
                body = await request.body()
                if not body:
                    return JSONResponse(status_code=400, content=response)
                request_params = json.loads(body.decode())
                if not request_params:
                    return JSONResponse(status_code=400, content=response)
            except:
                return JSONResponse(status_code=400, content=response)
            return await func(request, *args, **kwargs)

        validate.__signature__ = inspect.signature(func)
        return validate
    return method_validator
