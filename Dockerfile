# Base Image (Python 3.10 or latest version)
FROM python:3.10

# Set the working directory inside the container
WORKDIR /code

RUN pip install --upgrade pip setuptools wheel


# Copy the requirements file and install dependencies
COPY requirements.txt .

RUN pip install --no-cache-dir -r requirements.txt

# Copy the FastAPI app to the container
COPY . .

# Expose the port Fast<PERSON><PERSON> runs on
EXPOSE 9201

# Command to start FastAPI server
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "9201", "--reload"]
