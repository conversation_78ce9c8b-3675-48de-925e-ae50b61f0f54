/*************
  Dark Theme (by the<PERSON><PERSON>)
 *************/
/* overall */
.tablesorter-dark {
	width: 100%;
	font: 11px/18px Arial, Sans-serif;
	color: #ccc;
	text-align: left;
	background-color: #000;
	border-spacing: 0;
}

/* header */
.tablesorter-dark th,
.tablesorter-dark thead td {
	padding: 4px;
	font: bold 12px/20px Arial, Sans-serif;
	color: #fff;
	background-color: #000;
	border-collapse: collapse;
}
.tablesorter-dark thead th {
	border-bottom: #333 2px solid;
}
.tablesorter-dark .header,
.tablesorter-dark .tablesorter-header {
	padding: 4px 20px 4px 4px;
	cursor: pointer;
	background-image: url(data:image/gif;base64,R0lGODlhFQAJAIAAAP///////yH5BAEAAAEALAAAAAAVAAkAAAIXjI+AywnaYnhUMoqt3gZXPmVg94yJVQAAOw==);
	background-position: center right;
	background-repeat: no-repeat;
}
.tablesorter-dark thead .headerSortUp,
.tablesorter-dark thead .tablesorter-headerSortUp,
.tablesorter-dark thead .tablesorter-headerAsc {
	background-image: url(data:image/gif;base64,R0lGODlhFQAEAIAAAP///////yH5BAEAAAEALAAAAAAVAAQAAAINjI8Bya2wnINUMopZAQA7);
	border-bottom: #888 1px solid;
}
.tablesorter-dark thead .headerSortDown,
.tablesorter-dark thead .tablesorter-headerSortDown,
.tablesorter-dark thead .tablesorter-headerDesc {
	background-image: url(data:image/gif;base64,R0lGODlhFQAEAIAAAP///////yH5BAEAAAEALAAAAAAVAAQAAAINjB+gC+jP2ptn0WskLQA7);
	border-bottom: #888 1px solid;
}
.tablesorter-dark thead .sorter-false {
	background-image: url();
	padding: 4px;
}

/* tfoot */
.tablesorter-dark tfoot .tablesorter-headerSortUp,
.tablesorter-dark tfoot .tablesorter-headerSortDown,
.tablesorter-dark tfoot .tablesorter-headerAsc,
.tablesorter-dark tfoot .tablesorter-headerDesc {
	border-top: #888 1px solid;
	/* remove sort arrows from footer */
	background-image: url();
}

/* tbody */
.tablesorter-dark td {
	padding: 4px;
	background-color: #000;
	border-bottom: #333 1px solid;
	color: #ccc;
}

/* hovered row colors */
.tablesorter-dark tbody > tr:hover > td,
.tablesorter-dark tbody > tr.even:hover > td,
.tablesorter-dark tbody > tr.odd:hover > td {
	background: #000;
}

/* table processing indicator */
.tablesorter-dark .tablesorter-processing {
	background-position: center center !important;
	background-repeat: no-repeat !important;
	/* background-image: url(../addons/pager/icons/loading.gif) !important; */
	background-image: url('data:image/gif;base64,R0lGODlhFAAUAKEAAO7u7lpaWgAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQBCgACACwAAAAAFAAUAAACQZRvoIDtu1wLQUAlqKTVxqwhXIiBnDg6Y4eyx4lKW5XK7wrLeK3vbq8J2W4T4e1nMhpWrZCTt3xKZ8kgsggdJmUFACH5BAEKAAIALAcAAAALAAcAAAIUVB6ii7jajgCAuUmtovxtXnmdUAAAIfkEAQoAAgAsDQACAAcACwAAAhRUIpmHy/3gUVQAQO9NetuugCFWAAAh+QQBCgACACwNAAcABwALAAACE5QVcZjKbVo6ck2AF95m5/6BSwEAIfkEAQoAAgAsBwANAAsABwAAAhOUH3kr6QaAcSrGWe1VQl+mMUIBACH5BAEKAAIALAIADQALAAcAAAIUlICmh7ncTAgqijkruDiv7n2YUAAAIfkEAQoAAgAsAAAHAAcACwAAAhQUIGmHyedehIoqFXLKfPOAaZdWAAAh+QQFCgACACwAAAIABwALAAACFJQFcJiXb15zLYRl7cla8OtlGGgUADs=') !important;
}

/* Zebra Widget - row alternating colors */
.tablesorter-dark tr.odd td {
	background-color: #202020;
}
.tablesorter-dark tr.even td {
	background-color: #101010;
}

/* Column Widget - column sort colors */
.tablesorter-dark td.primary,
.tablesorter-dark tr.odd td.primary {
	background-color: #0a0a0a;
}
.tablesorter-dark tr.even td.primary {
	background-color: #050505;
}
.tablesorter-dark td.secondary,
.tablesorter-dark tr.odd td.secondary {
	background-color: #0f0f0f;
}
.tablesorter-dark tr.even td.secondary {
	background-color: #0a0a0a;
}
.tablesorter-dark td.tertiary,
.tablesorter-dark tr.odd td.tertiary {
	background-color: #191919;
}
.tablesorter-dark tr.even td.tertiary {
	background-color: #0f0f0f;
}

/* filter widget */
.tablesorter-dark .tablesorter-filter-row td {
	background: #202020;
	line-height: normal;
	text-align: center; /* center the input */
	-webkit-transition: line-height 0.1s ease;
	-moz-transition: line-height 0.1s ease;
	-o-transition: line-height 0.1s ease;
	transition: line-height 0.1s ease;
}
/* optional disabled input styling */
.tablesorter-dark .tablesorter-filter-row .disabled {
	opacity: 0.5;
	filter: alpha(opacity=50);
	cursor: not-allowed;
}

/* hidden filter row */
.tablesorter-dark .tablesorter-filter-row.hideme td {
	/*** *********************************************** ***/
	/*** change this padding to modify the thickness     ***/
	/*** of the closed filter row (height = padding x 2) ***/
	padding: 2px;
	/*** *********************************************** ***/
	margin: 0;
	line-height: 0;
	cursor: pointer;
}
.tablesorter-dark .tablesorter-filter-row.hideme .tablesorter-filter {
	height: 1px;
	min-height: 0;
	border: 0;
	padding: 0;
	margin: 0;
	/* don't use visibility: hidden because it disables tabbing */
	opacity: 0;
	filter: alpha(opacity=0);
}

/* filters */
.tablesorter-dark .tablesorter-filter {
	width: 98%;
	height: inherit;
	margin: 4px;
	padding: 4px;
	background-color: #111;
	border: 1px solid #222;
	color: #ddd;
	-webkit-box-sizing: border-box;
	-moz-box-sizing: border-box;
	box-sizing: border-box;
	-webkit-transition: height 0.1s ease;
	-moz-transition: height 0.1s ease;
	-o-transition: height 0.1s ease;
	transition: height 0.1s ease;
}
