# XSERP FastAPI

XSERP is a FastAPI-based application designed for high-performance and scalable backend services. This document provides instructions on building, running, and managing the application using Docker.

## Prerequisites

Ensure you have the following installed on your system before proceeding:

- [Docker](https://docs.docker.com/get-docker/)
- [Docker Compose](https://docs.docker.com/compose/install/)

### Getting Started

### 1. Build the Docker Image
To create a Docker image for the application, run:
```sh
docker build -t mobile_apis .
```

### 2. Running the Docker Container
To start the application using Docker Compose, run:
```sh
docker-compose up -d
```
This runs the container in detached mode (`-d`).

### 3. Stopping the Container
To stop the running container, execute:
```sh
docker-compose down
```

### 4. Restarting the Server
If you need to restart the application, use:
```sh
docker-compose restart
```

### 5. Checking Logs
To view real-time logs from the running container:
```sh
docker logs -f mobile_apis
```

### 6. Hard Reset
If you need to rebuild the image and restart everything:
```sh
Windows
docker-compose down ; docker-compose up -d --build

Linux/Mac
 docker compose -f compose.yml down && docker compose -f compose.yml up -d --build
```

#### Environment Variables
Ensure you have a `.env` file with the necessary configurations before running the application. Example:
```env
DATABASE_URL=postgresql://user:password@db_host:port/db_name
SECRET_KEY=our_secret_key_here
DEBUG=True
```

##### API Documentation
Once the application is running, access the auto-generated API documentation at:
- **Swagger UI**: [http://localhost:8000/docs]
- **ReDoc**: [http://localhost:8000/redoc]
- **API**: [http://localhost:8000/]
###### Contributing
If you'd like to contribute to the project, feel free to fork the repository and submit a pull request.

For any issues or support, please contact the project maintainers.

