import datetime
# for thingsboard
import json

import pymysql
import requests
from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse
from tb_service.onboarder import tb_url, logger
from tb_service.onboarder.service import TBConnection
from tb_service.config import DATABASES_NAME, DATABASES_USER, DATABASES_PASSWORD, DATABASES_HOST, DATABASES_PORT, \
    LOGIN_CREDENTIALS

# ILM_VARIANTS_PROFILE_MAPPING contains ilm variants with corresponding profile and its credentials length
ILM_VARIANTS_PROFILE_MAPPING = {"SLNNSGINSTDGX01A": ("ilm", 16), "SLNN4GQTLSTDG01A": ("ilm-4g", 15),
                                "SLNN4GQTLDM0701A": ("ilm-4g", 15)}

dev_no_prefix = {0: 'AA', 1: 'AB', 2: 'AC', 3: 'AD', 4: 'AE', 5: 'AF', 6: 'AG', 7: 'AH', 8: 'AI', 9: 'AJ',
                 10: 'AK', 11: 'AL', 12: 'AM', 13: 'AN', 14: 'AO', 15: 'AP', 16: 'AQ', 17: 'AR', 18: 'AS',
                 19: 'AT', 20: 'AU', 21: 'AV', 22: 'AW', 23: 'AX', 24: 'AY', 25: 'AZ', 26: 'BA', 27: 'BB',
                 28: 'BC', 29: 'BD', 30: 'BE', 31: 'BF', 32: 'BG', 33: 'BH', 34: 'BI', 35: 'BJ', 36: 'BK',
                 37: 'BL', 38: 'BM', 39: 'BN', 40: 'BO', 41: 'BP', 42: 'BQ', 43: 'BR', 44: 'BS', 45: 'BT',
                 46: 'BU', 47: 'BV', 48: 'BW', 49: 'BX', 50: 'BY', 51: 'BZ', 52: 'CA', 53: 'CB', 54: 'CC',
                 55: 'CD', 56: 'CE', 57: 'CF', 58: 'CG', 59: 'CH', 60: 'CI', 61: 'CJ', 62: 'CK', 63: 'CL',
                 64: 'CM', 65: 'CN', 66: 'CO', 67: 'CP', 68: 'CQ', 69: 'CR', 70: 'CS', 71: 'CT', 72: 'CU',
                 73: 'CV', 74: 'CW', 75: 'CX', 76: 'CY', 77: 'CZ', 78: 'DA', 79: 'DB', 80: 'DC', 81: 'DD',
                 82: 'DE', 83: 'DF', 84: 'DG', 85: 'DH', 86: 'DI', 87: 'DJ', 88: 'DK', 89: 'DL', 90: 'DM',
                 91: 'DN', 92: 'DO', 93: 'DP', 94: 'DQ', 95: 'DR', 96: 'DS', 97: 'DT', 98: 'DU', 99: 'DV',
                 100: 'DW'}

tb_user_data = LOGIN_CREDENTIALS

db_config = {
    'host': DATABASES_HOST,  # Usually 'localhost' for local development
    'user': DATABASES_USER,
    'password': DATABASES_PASSWORD,
    'db': DATABASES_NAME,
    'port': int(DATABASES_PORT),
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor,  # Return query results as dictionaries
}

router = APIRouter()

@router.get("/ilm/{credentials}/{variant}/")
async def add_ilm_device(request: Request, credentials: str, variant: str):
    new_device_no = ""
    device_profile_map = ILM_VARIANTS_PROFILE_MAPPING.get(variant, 'ilm')

    if len(credentials) != device_profile_map[1]:
        logger.info("Invalid IEEE Address")
        return JSONResponse(content="Please enter the valid IEEE Address and variant")

    try:
        conn = pymysql.connect(**db_config)
        cur = conn.cursor()
        cur.execute(
            "SELECT device_no, ieee_address, tb_id FROM device_master WHERE ieee_address=%s AND tb_id <> '-'",
            (credentials,)
        )
        device_data = cur.fetchall()
        conn.close()

        if device_data:
            try:
                return JSONResponse(content=device_data[0]['device_no'])
            except Exception as e:
                logger.exception("add_node_device 4008  : %s", str(e))
                return JSONResponse(content={"error_code": "4008"})
        else:
            conn = pymysql.connect(**db_config)
            cur = conn.cursor()
            cur.execute(
                "SELECT device_no FROM device_master WHERE ieee_address=%s",
                (credentials,)
            )
            existing_device = cur.fetchall()

            if existing_device:
                new_device_no = existing_device[0]['device_no']
            else:
                now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                cur.execute(
                    "INSERT INTO device_master (ieee_address, added_date) VALUES (%s, %s)",
                    (credentials, now)
                )
                conn.commit()

                cur.execute("SELECT * FROM device_master WHERE ieee_address=%s", (credentials,))
                did = cur.fetchall()
                dev_no = did[0]['did']
                dev_index_no = int(int(dev_no) / 1000)

                if dev_index_no:
                    new_device_no = 'Z' + str(dev_no_prefix[dev_index_no]) + str(dev_no - (1000 * dev_index_no)).zfill(3)
                else:
                    new_device_no = 'Z' + str(dev_no_prefix[dev_index_no]) + str(dev_no).zfill(3)

                cur.execute(
                    "UPDATE device_master SET device_no=%s WHERE ieee_address=%s",
                    (new_device_no, credentials)
                )
                conn.commit()
            conn.close()

            # Get TB Token
            tb_auth_token = TBConnection.get_tb_token(request, tb_user_data)
            header = {
                'Content-type': 'application/json',
                'Accept': '*/*',
                'X-Authorization': f"Bearer {tb_auth_token.get('token')}"
            }

            # Get entity group
            try:
                tb_get_entitygroup_response = requests.get(tb_url + "/entityGroups/DEVICE", headers=header, timeout=60)
                if "Token has expired" in tb_get_entitygroup_response.text:
                    tb_get_entitygroup_response = TBConnection.refresh_tb_token(
                        request, tb_url + "/entityGroups/DEVICE", 'get', tb_url, tb_user_data, ''
                    )
                    header['X-Authorization'] = f"Bearer {request.session['tb_token']}"

                tb_entitygroup_json_data = tb_get_entitygroup_response.json()
                for item in tb_entitygroup_json_data:
                    if 'All' in str(item.get('name')):
                        tb_entitygroup_id = item['id']['id']
                        break
                logger.info("Entitygroup_id : %s", tb_entitygroup_id)
            except Exception as e:
                logger.exception("add_node_device 4002 : %s", str(e))
                return JSONResponse(content={"error_code": "4002"})

            # Add Device
            try:
                device_data = {"name": new_device_no, "type": device_profile_map[0]}
                tb_device_response = requests.post(tb_url + "/device", headers=header, json=device_data, timeout=60)
                if "Token has expired" in tb_device_response.text:
                    tb_device_response = TBConnection.refresh_tb_token(
                        request, tb_url + "/device", 'post', tb_url, tb_user_data, device_data
                    )
                    header['X-Authorization'] = f"Bearer {request.session['tb_token']}"
                tb_device_id = tb_device_response.json()['id']['id']
            except Exception as e:
                logger.exception("add_node_device 4003 : %s", str(e))
                return JSONResponse(content={"error_code": "4003"})

            # Get Device Credentials
            try:
                tb_cred_url = f"{tb_url}/device/{tb_device_id}/credentials"
                tb_cred_response = requests.get(tb_cred_url, headers=header, timeout=60)
                if "Token has expired" in tb_cred_response.text:
                    tb_cred_response = TBConnection.refresh_tb_token(
                        request, tb_cred_url, 'get', tb_url, tb_user_data, ''
                    )
                    header['X-Authorization'] = f"Bearer {request.session['tb_token']}"
                tb_cred_data = tb_cred_response.json()
            except Exception as e:
                logger.exception("add_node_device 4004 : %s", str(e))
                return JSONResponse(content={"error_code": "4004"})

            # Update Credentials
            try:
                tb_cred_data['credentialsId'] = credentials
                tb_cred_data['credentialsValue'] = 'null'
                tb_change_cred_response = requests.post(
                    f"{tb_url}/device/credentials", headers=header,
                    data=json.dumps(tb_cred_data), timeout=60
                )
                if "Token has expired" in tb_change_cred_response.text:
                    tb_change_cred_response = TBConnection.refresh_tb_token(
                        request, tb_url + "/device/credentials", 'post', tb_url, tb_user_data, tb_cred_data
                    )
                    header['X-Authorization'] = f"Bearer {request.session['tb_token']}"
            except Exception as e:
                logger.exception("add_node_device 4004a : %s", str(e))
                return JSONResponse(content={"error_code": "4004"})

            # Add Server Attributes
            try:
                attributes_url = f"{tb_url}/plugins/telemetry/DEVICE/{tb_device_id}/SERVER_SCOPE"
                attributes_data = {
                    "inactivityTimeout": 1000000,
                    "testResults": {
                        "brightness_100": False,
                        "brightness_70": False,
                        "brightness_50": False,
                        "brightness_30": False,
                        "brightness_0": False,
                        "flash": False,
                        "rtc": False,
                        "error_count": 0
                    },
                    "dimmable": True,
                    "state": "ONBOARDED",
                    "condition": "NEW",
                    "qrCount": 0,
                    "boardNumber": new_device_no,
                    "variant": variant
                }
                requests.post(attributes_url, headers=header, data=json.dumps(attributes_data), timeout=60)
            except Exception as e:
                logger.exception("add_node_device 4005 : %s", str(e))
                return JSONResponse(content={"error_code": "4005"})

            # Update DB with tb_id
            try:
                conn = pymysql.connect(**db_config)
                cur = conn.cursor()
                cur.execute(
                    "UPDATE device_master SET tb_id = %s WHERE ieee_address = %s",
                    (tb_device_id, credentials)
                )
                conn.commit()
                conn.close()
            except Exception as e:
                logger.exception("add_node_device 4006 : %s", str(e))
                return JSONResponse(content={"error_code": "4006"})

    except Exception as e:
        logger.exception("add_device --> %s", str(e))

    logger.info("new_device_no -->  %s", str(new_device_no))
    return JSONResponse(content=new_device_no)


@router.post("/add_gateway_device/{imei_no}/{variant}")
async def add_gateway_device(request: Request, imei_no: str, variant: str):
    new_device_no = ""
    try:
        conn = pymysql.connect(**db_config)
        cur = conn.cursor()
        isql = "SELECT board_no, imei_no, tb_production_id FROM gateway_device_master WHERE imei_no = %s AND tb_production_id <> '-'"
        cur.execute(isql, (imei_no,))
        device_no = cur.fetchall()
        conn.close()

        if len(device_no) >= 1:
            return JSONResponse(content=device_no[0]['board_no'])

        # Check if imei already exists to get board_no
        conn = pymysql.connect(**db_config)
        cur = conn.cursor()
        isql = "SELECT board_no FROM gateway_device_master WHERE imei_no = %s"
        cur.execute(isql, (imei_no,))
        dev_data = cur.fetchall()

        if len(dev_data) > 0:
            new_device_no = dev_data[0]['board_no']
        else:
            isql = "INSERT INTO gateway_device_master (imei_no, datetime) VALUES (%s, %s)"
            cur.execute(isql, (imei_no, datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')))
            conn.commit()

            isql = "SELECT * FROM gateway_device_master WHERE imei_no = %s"
            cur.execute(isql, (imei_no,))
            did = cur.fetchall()
            dev_no = did[0]['gid']
            dev_index_no = int(int(dev_no) / 1000)

            if dev_index_no:
                new_device_no = '#' + str(dev_no_prefix[dev_index_no]) + str(dev_no - (1000 * dev_index_no)).zfill(3)
            else:
                new_device_no = '#' + str(dev_no_prefix[dev_index_no]) + str(dev_no).zfill(3)

            isql = "UPDATE gateway_device_master SET board_no = %s WHERE imei_no = %s"
            cur.execute(isql, (new_device_no, imei_no))
            conn.commit()
        conn.close()

        # TB Token
        tb_auth_token = TBConnection.get_tb_token(request, tb_user_data)
        header = {
            'Content-type': 'application/json',
            'Accept': '*/*',
            'X-Authorization': f"Bearer {tb_auth_token.get('token')}"
        }

        # Entity Group
        try:
            tb_api_url = f"{tb_url}/entityGroups/DEVICE"
            tb_get_entitygroup_response = requests.get(tb_api_url, headers=header, timeout=60)
            if "Token has expired" in tb_get_entitygroup_response.text:
                tb_get_entitygroup_response = TBConnection.refresh_tb_token(
                    request, tb_api_url, 'get', tb_url, tb_user_data, ''
                )
                header['X-Authorization'] = f"Bearer {request.session['tb_token']}"
            tb_entitygroup_json_data = tb_get_entitygroup_response.json()
            tb_entitygroup_id = next(
                (item.get('id', {}).get('id') for item in tb_entitygroup_json_data if "All" in item.get('name', '')),
                None
            )
        except Exception as e:
            logger.exception("add_gateway_device 4002 --> %s", str(e))
            return JSONResponse(content={'error_code': '4002'}, status_code=500)

        # Add device
        try:
            tb_api_url = f"{tb_url}/device?entityGroupId={tb_entitygroup_id}"
            data = json.dumps({"name": new_device_no, "type": "gw"})
            tb_get_device_response = requests.post(tb_api_url, headers=header, data=data, timeout=60)
            if "Token has expired" in tb_get_device_response.text:
                tb_get_device_response = TBConnection.refresh_tb_token(
                    request, tb_api_url, 'post', tb_url, tb_user_data, data
                )
                header['X-Authorization'] = f"Bearer {request.session['tb_token']}"
            tb_device_id = tb_get_device_response.json().get('id', {}).get('id')
            logger.info("Device_id: %s", str(tb_device_id))
        except Exception as e:
            logger.exception("add_gateway_device 4003 --> %s", str(e))
            return JSONResponse(content={'error_code': '4003'}, status_code=500)

        # Get credentials
        try:
            apiurl1 = f"{tb_url}/device/{tb_device_id}/credentials"
            tb_get_device_credentials_response = requests.get(apiurl1, headers=header, timeout=60)
            if "Token has expired" in tb_get_device_credentials_response.text:
                tb_get_device_credentials_response = TBConnection.refresh_tb_token(
                    request, apiurl1, 'get', tb_url, tb_user_data, ''
                )
                header['X-Authorization'] = f"Bearer {request.session['tb_token']}"
            tb_device_credentials_json_data = tb_get_device_credentials_response.json()
        except Exception as e:
            logger.exception("add_gateway_device 4004 --> %s", str(e))
            return JSONResponse(content={'error_code': '4004'}, status_code=500)

        # Change credentials
        try:
            tb_device_credentials_json_data['credentialsId'] = imei_no
            tb_device_credentials_json_data['credentialsValue'] = 'null'
            apiurl1 = f"{tb_url}/device/credentials"
            data = json.dumps(tb_device_credentials_json_data)
            tb_change_device_credentials_response = requests.post(apiurl1, headers=header, data=data, timeout=60)
            if "Token has expired" in tb_change_device_credentials_response.text:
                tb_change_device_credentials_response = TBConnection.refresh_tb_token(
                    request, apiurl1, 'post', tb_url, tb_user_data, data
                )
                header['X-Authorization'] = f"Bearer {request.session['tb_token']}"
        except Exception as e:
            logger.exception("add_gateway_device 40041 --> %s", str(e))
            return JSONResponse(content={'error_code': '4004'}, status_code=500)

        # Add attributes
        try:
            logger.info('server attribute updating')
            apiurl1 = f"{tb_url}/plugins/telemetry/DEVICE/{tb_device_id}/SERVER_SCOPE"
            data = json.dumps({
                "state": "ONBOARDED",
                "condition": "NEW",
                "inactivityTimeout": 2100000,
                "boardNumber": new_device_no,
                "variant": variant
            })
            tb_add_attributes_response = requests.post(apiurl1, headers=header, data=data, timeout=60)
            logger.info('server_attribute_insert: %s', str(tb_add_attributes_response.text))
        except Exception as e:
            logger.exception("add_gateway_device 4005 --> %s", str(e))
            return JSONResponse(content={'error_code': '4005'}, status_code=500)

        # Final update in DB
        try:
            conn = pymysql.connect(**db_config)
            cur = conn.cursor()
            isql = "UPDATE gateway_device_master SET tb_production_id = %s WHERE imei_no = %s"
            cur.execute(isql, (tb_device_id, imei_no))
            conn.commit()
            conn.close()
        except Exception as e:
            logger.exception("add_gateway_device 4006 --> %s", str(e))
            return JSONResponse(content={'error_code': '4006'}, status_code=500)

    except Exception as e:
        logger.exception("add_gateway_device 4009 --> %s", str(e))

    return JSONResponse(content=new_device_no)