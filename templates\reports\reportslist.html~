<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
	<title>ILM Log </title>	
	<link rel="stylesheet" href="../../site_media/css/styles/livestatusreport.css">
	<link class="ui-theme" rel="stylesheet" href="../../site_media/css/styles/theme.jui.css">
	<link class="ui-theme" rel="stylesheet" href="../../site_media/css/styles/theme.blue.css">
	<script src="../../site_media/js/jquery.js"></script>
	<script src="../../site_media/js/report_fix_header/jquery.chili-2.2.js"></script>
	<script src="../../site_media/js/report_fix_header/recipes.js"></script>
	<script src="../../site_media/js/report_fix_header/docs.js"></script>	
	<script src="../../site_media/js/report_fix_header/jquery.tablesorter.js"></script>
	<script src="../../site_media/js/report_fix_header/jquery.tablesorter.widgets.js"></script>

	<script id="js">$(function(){

	$("table").tablesorter({

		widthFixed : true,
		widgets: ["columns", "stickyHeaders", "uitheme", "zebra"] , 
		widgetOptions: {
			stickyHeaders : 'tablesorter-stickyHeader',
			zebra   : ["ui-widget-content even", "ui-state-default odd"],
			uitheme : 'blue'
		}

	});

}); </script>
</head>
<body>
<form method="post">{% csrf_token %}
<div id="banner">
	<h1><em>ILM Log </em> </h1>
	<br>
</div>
<div id="main">
<div id="demo"><table class="tablesorter">
	<thead>
		<tr>
			<th rowspan="2" align="center">Date</th>
			<th rowspan="2" align="center">Packet</th>
		</tr>
	</thead>
	<tbody>
		{% for i in data %}
	<tr>
				<td align="left">{{ i.0|date:'d/m/y H:i' }}</td>
	                       <td align="left">{{ i.1}}</td>
		    
       </tr>
        {% endfor %}
	</tbody>
</table></div>
</div>
</form>
</body>
</html>
